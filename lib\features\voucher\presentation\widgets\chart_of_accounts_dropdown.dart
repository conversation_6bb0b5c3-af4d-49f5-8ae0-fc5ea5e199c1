import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../models/finance/chart_of_accounts_model.dart';
import '../../../accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';

/// Reusable dropdown widget for Chart of Accounts selection
/// Replaces legacy AccountModel dropdowns in voucher system
class ChartOfAccountsDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final List<AccountCategory>? allowedCategories;
  final List<AccountType>? allowedTypes;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;
  final bool showAccountNumber;
  final bool showBalance;
  final bool activeOnly;

  const ChartOfAccountsDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.allowedCategories,
    this.allowedTypes,
    this.isRequired = false,
    this.validator,
    this.showAccountNumber = true,
    this.showBalance = false,
    this.activeOnly = true,
  });

  @override
  Widget build(BuildContext context) {
    return GetX<ChartOfAccountsController>(
      builder: (controller) {
        // Debug logging
        log('ChartOfAccountsDropdown: Building with ${controller.allAccounts.length} accounts');
        log('ChartOfAccountsDropdown: Widget label: $labelText');
        log('ChartOfAccountsDropdown: Allowed categories: $allowedCategories');
        log('ChartOfAccountsDropdown: Allowed types: $allowedTypes');

        // Filter accounts based on criteria
        final filteredAccounts = _filterAccounts(controller.allAccounts);
        log('ChartOfAccountsDropdown: Filtered to ${filteredAccounts.length} accounts');

        // Debug: Log filtered account details
        for (var account in filteredAccounts.take(5)) {
          log('ChartOfAccountsDropdown: Filtered account - ${account.accountName} (${account.accountCategory.displayName})');
        }

        // If no accounts available, show a message
        if (filteredAccounts.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'No accounts available. Please create accounts in Chart of Accounts.',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              ],
            ),
          );
        }

        return DropdownButtonFormField<ChartOfAccountsModel>(
          decoration: InputDecoration(
            labelText: labelText,
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.transparent,
            prefixIcon: Icon(
              _getCategoryIcon(),
              color: Theme.of(context).primaryColor,
            ),
          ),
          value: selectedAccount,
          validator: validator ??
              (value) {
                if (isRequired && value == null) {
                  return 'Please select an account';
                }
                return null;
              },
          onChanged: onChanged,
          items: filteredAccounts.map((account) {
            return DropdownMenuItem<ChartOfAccountsModel>(
              value: account,
              child: _buildAccountItem(account, context),
            );
          }).toList(),
          isExpanded: true,
          menuMaxHeight: 300,
        );
      },
    );
  }

  /// Filter accounts based on allowed categories and types
  List<ChartOfAccountsModel> _filterAccounts(
      List<ChartOfAccountsModel> accounts) {
    log('ChartOfAccountsDropdown: Filtering ${accounts.length} accounts');
    log('ChartOfAccountsDropdown: Allowed categories: ${allowedCategories?.map((c) => c.displayName).join(', ') ?? 'ALL'}');
    log('ChartOfAccountsDropdown: Allowed types: ${allowedTypes?.map((t) => t.displayName).join(', ') ?? 'ALL'}');
    log('ChartOfAccountsDropdown: Active only: $activeOnly');

    final filtered = accounts.where((account) {
      // Filter by active status
      if (activeOnly && !account.isActive) {
        log('ChartOfAccountsDropdown: Filtered out ${account.accountName} - inactive');
        return false;
      }

      // Filter by allowed categories
      if (allowedCategories != null &&
          !allowedCategories!.contains(account.category)) {
        log('ChartOfAccountsDropdown: Filtered out ${account.accountName} - category mismatch (${account.category.displayName})');
        return false;
      }

      // Filter by allowed types
      if (allowedTypes != null &&
          !allowedTypes!.contains(account.accountType)) {
        log('ChartOfAccountsDropdown: Filtered out ${account.accountName} - type mismatch (${account.accountType.displayName})');
        return false;
      }

      log('ChartOfAccountsDropdown: Included ${account.accountName} (${account.category.displayName})');
      return true;
    }).toList();

    log('ChartOfAccountsDropdown: Final filtered count: ${filtered.length}');
    return filtered;
  }

  /// Build individual account item widget
  Widget _buildAccountItem(ChartOfAccountsModel account, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            // Account status indicator
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: account.isActive ? Colors.green : Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            // Account number and name
            Expanded(
              child: Text(
                showAccountNumber ? account.displayName : account.accountName,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Balance display (if enabled)
            if (showBalance) ...[
              const SizedBox(width: 8),
              Text(
                '\$${account.balance.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: account.balance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        // Account type and category
        Padding(
          padding: const EdgeInsets.only(left: 16, top: 2),
          child: Text(
            '${account.accountType.displayName} • ${account.category.displayName}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  /// Get appropriate icon based on allowed categories
  IconData _getCategoryIcon() {
    if (allowedCategories == null || allowedCategories!.isEmpty) {
      return Icons.account_balance;
    }

    // Return icon for the first allowed category
    return allowedCategories!.first.icon;
  }
}

/// Specialized dropdown for expense accounts (broker fees, munshiana, etc.)
class ExpenseAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const ExpenseAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.expenses],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for asset accounts (cash, bank accounts for payments)
class AssetAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const AssetAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.assets],
      allowedTypes: const [
        AccountType.cash,
        AccountType.bank,
        AccountType.currentAssets,
        AccountType.accountsReceivable,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance:
          false, // Hide balance for voucher forms - not needed for account selection
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for revenue accounts
class RevenueAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const RevenueAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.revenue],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for liability accounts (tax accounts, payables)
class LiabilityAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const LiabilityAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.liabilities],
      allowedTypes: const [
        AccountType.currentLiabilities,
        AccountType.accountsPayable,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for equity service revenue accounts only
class EquityAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const EquityAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.equity],
      allowedTypes: const [
        AccountType.equityServiceRevenue,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for retained earnings accounts only
class RetainedEarningsAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const RetainedEarningsAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.equity],
      allowedTypes: const [
        AccountType.retainedEarnings,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for Loan Receivable accounts (Asset accounts for money lent out)
class LoanReceivableAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final ValueChanged<ChartOfAccountsModel?> onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const LoanReceivableAccountDropdown({
    super.key,
    this.labelText = 'Loan Receivable Account',
    this.hintText = 'Select account for tracking money lent out',
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = true,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.assets],
      allowedTypes: const [
        AccountType.currentAssets,
        AccountType.accountsReceivable,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for Loan Payable accounts (Liability accounts for money borrowed)
class LoanPayableAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final ValueChanged<ChartOfAccountsModel?> onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const LoanPayableAccountDropdown({
    super.key,
    this.labelText = 'Loan Payable Account',
    this.hintText = 'Select account for tracking money borrowed',
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = true,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.liabilities],
      allowedTypes: const [
        AccountType.currentLiabilities,
        AccountType.longTermLiabilities,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}

/// Specialized dropdown for truck/wagon freight expense accounts (Liability accounts)
class TruckFreightAccountDropdown extends StatelessWidget {
  final String labelText;
  final String hintText;
  final ChartOfAccountsModel? selectedAccount;
  final Function(ChartOfAccountsModel?) onChanged;
  final bool isRequired;
  final String? Function(ChartOfAccountsModel?)? validator;

  const TruckFreightAccountDropdown({
    super.key,
    required this.labelText,
    required this.hintText,
    required this.selectedAccount,
    required this.onChanged,
    this.isRequired = false,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return ChartOfAccountsDropdown(
      labelText: labelText,
      hintText: hintText,
      selectedAccount: selectedAccount,
      onChanged: onChanged,
      allowedCategories: const [AccountCategory.liabilities],
      allowedTypes: const [
        AccountType.accountsPayable,
        AccountType.currentLiabilities,
      ],
      isRequired: isRequired,
      validator: validator,
      showAccountNumber: true,
      showBalance: false,
      activeOnly: true,
    );
  }
}
