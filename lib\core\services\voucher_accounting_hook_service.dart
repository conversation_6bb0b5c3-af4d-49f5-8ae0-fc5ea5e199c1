import 'dart:developer';
import '../../models/voucher_model.dart';
import '../../models/payment_transaction_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/check_usage_model.dart';
import '../../firebase_service/accounting/general_ledger_firebase_service.dart';
import '../../firebase_service/accounting/chart_of_accounts_firebase_service.dart';
import '../../firebase_service/accounting/journal_entry_firebase_service.dart';
import '../../firebase_service/finance/check_usage_firebase_service.dart';
import '../../firebase_service/finance/company_firebase_service.dart';
import '../../features/finance/check_usage/repositories/check_usage_repository.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'automatic_journal_entry_service.dart';
import 'transaction_account_mapping_service.dart';
import 'voucher_journal_integration_service.dart';
import 'account_ledger_service.dart';
import 'account_type_helper_service.dart';
import 'voucher_loan_integration_service.dart';
import 'broker_financial_service.dart';
import 'package:uuid/uuid.dart';
import 'package:get/get.dart';

/// Service that hooks into voucher creation and updates to automatically generate journal entries
class VoucherAccountingHookService {
  static VoucherAccountingHookService? _instance;
  late final VoucherJournalIntegrationService _integrationService;
  late final AutomaticJournalEntryService _automaticJournalService;
  late final GeneralLedgerFirebaseService _generalLedgerService;
  late final JournalEntryFirebaseService _journalEntryService;
  late final TransactionAccountMappingService _mappingService;
  late final ChartOfAccountsFirebaseService _chartOfAccountsService;
  late final VoucherLoanIntegrationService _loanIntegrationService;
  BrokerFinancialService? _brokerFinancialService;

  VoucherAccountingHookService._internal() {
    _initializeServices();
  }

  factory VoucherAccountingHookService() {
    _instance ??= VoucherAccountingHookService._internal();
    return _instance!;
  }

  void _initializeServices() {
    _chartOfAccountsService = ChartOfAccountsFirebaseService();
    _generalLedgerService = GeneralLedgerFirebaseService();
    _journalEntryService = JournalEntryFirebaseService();
    _mappingService = TransactionAccountMappingService(_chartOfAccountsService);
    _automaticJournalService = AutomaticJournalEntryService(
      _chartOfAccountsService,
    );
    _integrationService = VoucherJournalIntegrationService(
      _automaticJournalService,
      _generalLedgerService,
      _journalEntryService,
      _mappingService,
      AccountLedgerService(),
    );
    _loanIntegrationService = VoucherLoanIntegrationService();

    // Initialize broker financial service using Get.find (lazy initialization)
    try {
      _brokerFinancialService = Get.find<BrokerFinancialService>();
    } catch (e) {
      log('Warning: BrokerFinancialService not available: $e');
    }
  }

  /// Hook method to be called after voucher creation
  Future<void> onVoucherCreated(
      Map<String, dynamic> voucherData, String uid) async {
    log('🔗 VoucherAccountingHookService: Hook triggered for voucher: ${voucherData['voucherNumber']}');
    log('📊 VoucherAccountingHookService: Voucher data keys: ${voucherData.keys.toList()}');

    // NOTE: Account validation is now handled by VoucherJournalIntegrationService
    // which checks user-selected accounts vs default mappings appropriately

    // Convert to VoucherModel for processing
    VoucherModel voucher;
    try {
      voucher = VoucherModel.fromJson(voucherData);
      log('✅ VoucherAccountingHookService: VoucherModel conversion successful');
    } catch (e) {
      log('❌ VoucherAccountingHookService: Failed to convert voucher data to model: $e');
      log('📋 VoucherAccountingHookService: Voucher data: $voucherData');
      throw Exception('Failed to convert voucher data to model: $e');
    }

    // Validate voucher for journal entry generation
    log('🔍 VoucherAccountingHookService: Validating voucher for journal entry generation...');
    final validation =
        await _integrationService.validateVoucherForJournalEntry(voucher, uid);
    if (!validation.isValid) {
      log('❌ VoucherAccountingHookService: Voucher validation failed: ${validation.issuesText}');
      throw Exception('Voucher validation failed: ${validation.issuesText}');
    }
    log('✅ VoucherAccountingHookService: Voucher validation passed');

    // Check if journal entries already exist
    log('🔍 VoucherAccountingHookService: Checking for existing journal entries...');
    final hasExisting = await _integrationService.hasExistingJournalEntries(
      voucher.voucherNumber,
      uid,
    );

    if (hasExisting) {
      log('⚠️ VoucherAccountingHookService: Journal entries already exist for voucher: ${voucher.voucherNumber}');
      return; // This is not an error, just skip processing
    }
    log('✅ VoucherAccountingHookService: No existing journal entries found');

    // Process the voucher transaction and payment transactions together for proper sequencing
    log('🔄 VoucherAccountingHookService: Processing voucher and payment transactions with sequential numbering...');
    await _processVoucherAndPaymentTransactionsSequentially(voucher, uid);
    log('✅ VoucherAccountingHookService: Successfully processed all transactions for voucher: ${voucher.voucherNumber}');

    // Record broker fee transaction if applicable
    await _recordBrokerFeeTransaction(voucher, uid);
  }

  /// Hook method to be called after voucher creation (VoucherModel version)
  Future<void> onVoucherCreatedFromModel(
      VoucherModel voucher, String uid) async {
    log('Voucher accounting hook triggered for model: ${voucher.voucherNumber}');

    // Validate voucher for journal entry generation
    final validation =
        await _integrationService.validateVoucherForJournalEntry(voucher, uid);
    if (!validation.isValid) {
      log('Voucher validation failed: ${validation.issuesText}');
      throw Exception('Voucher validation failed: ${validation.issuesText}');
    }

    // Check if journal entries already exist
    final hasExisting = await _integrationService.hasExistingJournalEntries(
      voucher.voucherNumber,
      uid,
    );

    if (hasExisting) {
      log('Journal entries already exist for voucher: ${voucher.voucherNumber}');
      return; // This is not an error, just skip processing
    }

    // Process the voucher transaction
    final success =
        await _integrationService.processVoucherTransaction(voucher, uid);
    if (!success) {
      log('Failed to create journal entries for voucher: ${voucher.voucherNumber}');
      throw Exception(
          'Failed to create journal entries for voucher: ${voucher.voucherNumber}');
    }
    log('Successfully created journal entries for voucher: ${voucher.voucherNumber}');

    // Record broker fee transaction if applicable
    await _recordBrokerFeeTransaction(voucher, uid);
  }

  /// Hook method to be called before voucher deletion
  Future<void> onVoucherDeleted(
      Map<String, dynamic> voucherData, String uid) async {
    try {
      final voucherNumber = voucherData['voucherNumber'] as String? ?? '';
      log('Voucher deletion hook triggered for: $voucherNumber');

      if (voucherNumber.isEmpty) {
        log('Invalid voucher number for deletion hook');
        return;
      }

      // Reverse any existing journal entries
      final success = await _integrationService.reverseVoucherJournalEntries(
        voucherNumber,
        uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted voucher: $voucherNumber');
      } else {
        log('Failed to reverse journal entries for deleted voucher: $voucherNumber');
      }
    } catch (e) {
      log('Error in voucher deletion hook: $e');
    }
  }

  /// Hook method to be called before voucher deletion (VoucherModel version)
  Future<void> onVoucherDeletedFromModel(
      VoucherModel voucher, String uid) async {
    try {
      log('Voucher deletion hook triggered for model: ${voucher.voucherNumber}');

      // Reverse any existing journal entries
      final success = await _integrationService.reverseVoucherJournalEntries(
        voucher.voucherNumber,
        uid,
      );

      if (success) {
        log('Successfully reversed journal entries for deleted voucher: ${voucher.voucherNumber}');
      } else {
        log('Failed to reverse journal entries for deleted voucher: ${voucher.voucherNumber}');
      }
    } catch (e) {
      log('Error in voucher deletion hook: $e');
    }
  }

  /// Hook method to be called after voucher update
  Future<void> onVoucherUpdated(
    Map<String, dynamic> oldVoucherData,
    Map<String, dynamic> newVoucherData,
    String uid,
  ) async {
    try {
      final voucherNumber = newVoucherData['voucherNumber'] as String? ?? '';
      log('Voucher update hook triggered for: $voucherNumber');

      // Convert to models for comparison
      VoucherModel oldVoucher, newVoucher;
      try {
        oldVoucher = VoucherModel.fromJson(oldVoucherData);
        newVoucher = VoucherModel.fromJson(newVoucherData);
      } catch (e) {
        log('Failed to convert voucher data to models: $e');
        return;
      }

      // Check if financial amounts changed
      if (_hasFinancialChanges(oldVoucher, newVoucher)) {
        log('Financial changes detected, processing incremental updates');

        // Check if only new payments were added (no voucher-level changes)
        bool hasVoucherChanges =
            oldVoucher.totalFreight != newVoucher.totalFreight ||
                oldVoucher.brokerFees != newVoucher.brokerFees ||
                oldVoucher.munshianaFees != newVoucher.munshianaFees ||
                oldVoucher.brokerAccount != newVoucher.brokerAccount ||
                oldVoucher.munshianaAccount != newVoucher.munshianaAccount;

        if (hasVoucherChanges) {
          log('Voucher-level changes detected, reversing all entries and recreating');
          // Reverse old entries
          await _integrationService.reverseVoucherJournalEntries(
            oldVoucher.voucherNumber,
            uid,
          );

          // Create new entries
          await onVoucherCreatedFromModel(newVoucher, uid);
        } else {
          log('Only new payments detected, processing incrementally');
          // Only process new payment transactions
          await _processNewPaymentTransactionsOnly(oldVoucher, newVoucher, uid);
        }
      }
    } catch (e) {
      log('Error in voucher update hook: $e');
    }
  }

  /// Check if voucher has financial changes that require journal entry updates
  bool _hasFinancialChanges(VoucherModel oldVoucher, VoucherModel newVoucher) {
    // Check voucher-level financial changes
    bool hasVoucherChanges =
        oldVoucher.totalFreight != newVoucher.totalFreight ||
            oldVoucher.brokerFees != newVoucher.brokerFees ||
            oldVoucher.munshianaFees != newVoucher.munshianaFees ||
            oldVoucher.brokerAccount != newVoucher.brokerAccount ||
            oldVoucher.munshianaAccount != newVoucher.munshianaAccount;

    // Check for new payment transactions
    bool hasNewPayments = _hasNewPaymentTransactions(oldVoucher, newVoucher);

    log('Financial changes check:');
    log('  - Voucher-level changes: $hasVoucherChanges');
    log('  - New payments: $hasNewPayments');

    return hasVoucherChanges || hasNewPayments;
  }

  /// Check if there are new payment transactions added to the voucher
  bool _hasNewPaymentTransactions(
      VoucherModel oldVoucher, VoucherModel newVoucher) {
    // If new voucher has more payments than old voucher, there are new payments
    if (newVoucher.paymentTransactions.length >
        oldVoucher.paymentTransactions.length) {
      log('New payments detected: ${newVoucher.paymentTransactions.length} vs ${oldVoucher.paymentTransactions.length}');
      return true;
    }

    // Check if any payment IDs in new voucher don't exist in old voucher
    final oldPaymentIds = oldVoucher.paymentTransactions
        .map((p) => PaymentTransactionModel.fromMap(p).id)
        .toSet();

    final newPaymentIds = newVoucher.paymentTransactions
        .map((p) => PaymentTransactionModel.fromMap(p).id)
        .toSet();

    final addedPayments = newPaymentIds.difference(oldPaymentIds);

    if (addedPayments.isNotEmpty) {
      log('New payment IDs detected: ${addedPayments.join(', ')}');
      return true;
    }

    return false;
  }

  /// Process only new payment transactions for incremental updates
  Future<void> _processNewPaymentTransactionsOnly(
      VoucherModel oldVoucher, VoucherModel newVoucher, String uid) async {
    try {
      log('🔄 Processing new payment transactions only for voucher: ${newVoucher.voucherNumber}');

      // Get existing payment IDs
      final oldPaymentIds = oldVoucher.paymentTransactions
          .map((p) => PaymentTransactionModel.fromMap(p).id)
          .toSet();

      // Find new payment transactions
      final newPayments = <PaymentTransactionModel>[];
      for (final paymentMap in newVoucher.paymentTransactions) {
        final payment = PaymentTransactionModel.fromMap(paymentMap);
        if (!oldPaymentIds.contains(payment.id)) {
          newPayments.add(payment);
          log('📝 Found new payment: ${payment.id} - ${payment.method} - ${payment.amount}');
        }
      }

      if (newPayments.isEmpty) {
        log('⚠️ No new payments found to process');
        return;
      }

      log('💳 Processing ${newPayments.length} new payment transactions');

      // Process each new payment transaction with complete workflow
      int journalEntriesCreated = 0;
      for (final payment in newPayments) {
        try {
          log('💰 Processing new payment ${payment.id}: ${payment.method} amount: ${payment.amount}');
          log('🔍 Payment details - AccountId: ${payment.accountId}, AccountName: ${payment.accountName}');
          log('🗓️ Payment transaction date: ${payment.transactionDate}');

          // Assign default Chart of Accounts for payments without account reference
          PaymentTransactionModel processedPayment = payment;
          if (payment.accountId == null || payment.accountId!.isEmpty) {
            log('🔧 Payment ${payment.id} missing Chart of Accounts - attempting to assign default account');
            processedPayment = await _assignDefaultAccountToPayment(payment);

            if (processedPayment.accountId == null ||
                processedPayment.accountId!.isEmpty) {
              log('⚠️ SKIPPING payment ${payment.id} - could not assign default Chart of Accounts');
              log('⚠️ Payment method: ${payment.method.name}, Amount: ${payment.amount}');
              log('⚠️ This payment will NOT generate a journal entry');
              continue;
            } else {
              log('✅ Assigned default account to payment ${payment.id}: ${processedPayment.accountName}');
            }
          }

          // Preserve the user-selected payment transaction date instead of overriding with voucher date
          log('✅ Preserving user-selected payment transaction date: ${processedPayment.transactionDate}');

          // Handle check usage tracking for check payments
          if (processedPayment.method == PaymentMethod.check &&
              processedPayment.checkNumber != null &&
              processedPayment.checkNumber!.isNotEmpty) {
            await _createCheckUsageRecord(processedPayment, newVoucher);
          }

          // Check if this payment should use loan-based workflow
          final shouldUseLoanWorkflow = _loanIntegrationService
              .shouldUseLoanBasedWorkflow(processedPayment.method);

          log('🔍 DEBUG: Payment method: ${processedPayment.method}');
          log('🔍 DEBUG: Should use loan workflow: $shouldUseLoanWorkflow');

          if (shouldUseLoanWorkflow) {
            // Check if this is an "Other" payment type based on account ownership
            final isOtherPaymentType =
                await _isOtherPaymentType(processedPayment);

            log('🔍 DEBUG: Is Other payment type (cross-company): $isOtherPaymentType');

            // Create pending loan requests for "Other" payment types (cross-company)
            if (isOtherPaymentType) {
              log('🔄 Using pending loan request workflow for "Other" payment: ${payment.id}');

              // Create pending loan request (requires approval from other company)
              final loanRequestResult =
                  await _loanIntegrationService.createLoanRequestFromPayment(
                payment: processedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (loanRequestResult.isLeft) {
                log('❌ Failed to create loan request for payment: ${payment.id}');
                continue;
              }

              final updatedPayment = loanRequestResult.right;
              log('✅ Created pending loan request for payment: ${payment.id}');

              // Create auto-posted payment journal entry (will be completed upon loan approval)
              final pendingJournalResult = await _loanIntegrationService
                  .createAutoPostedPaymentJournalEntry(
                payment: updatedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (pendingJournalResult.isLeft) {
                log('❌ Failed to create pending journal entry for payment: ${payment.id}');
                // Note: Loan request is already created, so we don't rollback here
                // The loan request will exist but without journal entry
                continue;
              }

              log('✅ Created pending journal entry for payment: ${payment.id}');
              log('🎯 Pending loan request ${updatedPayment.loanRequestId} should appear in Loan Requests and require approval from other company');
            } else {
              // "Own" payment type - use traditional workflow instead of loan workflow
              log('🔄 Own payment type detected - using traditional workflow for payment: ${payment.id}');
              log('✅ Skipping loan request creation for same-company payment');

              // Traditional workflow: Generate and create journal entry immediately
              final journalEntry =
                  await _automaticJournalService.generatePaymentJournalEntry(
                payment: processedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (journalEntry == null) {
                log('❌ Failed to generate journal entry for Own payment: ${payment.id}');
                continue;
              }

              // Create the journal entry in Firebase
              try {
                await _journalEntryService.createJournalEntry(journalEntry);
                log('✅ Created traditional journal entry for Own payment: ${payment.id}');
                log('📊 Journal entry details: ${journalEntry.lines.length} lines, Total Debits: ${journalEntry.totalDebits}, Total Credits: ${journalEntry.totalCredits}');

                // Update account balances
                await _updateAccountBalancesForPaymentJournalEntry(
                    journalEntry, uid);

                // Create ledger entries
                final ledgerService = AccountLedgerService();
                final ledgerSuccess = await ledgerService
                    .createLedgerEntriesFromJournalEntry(journalEntry);

                if (!ledgerSuccess) {
                  log('❌ Failed to create ledger entries for payment journal entry: ${journalEntry.entryNumber}');
                } else {
                  log('✅ Created ledger entries for payment: ${payment.id}');
                }
              } catch (e) {
                log('❌ Failed to save journal entry for Own payment: ${payment.id} - $e');
                continue;
              }
            }

            // Note: Journal entry is posted immediately, loan approval will create additional disbursement entries
            journalEntriesCreated++;
          } else {
            // Non-loan workflow: Create journal entry using the original simplified approach
            final journalEntry =
                await _automaticJournalService.generatePaymentJournalEntry(
              payment: processedPayment,
              uid: uid,
              createdBy:
                  'system', // Use system as creator for automatic updates
            );

            if (journalEntry == null) {
              log('❌ Failed to generate journal entry for payment: ${payment.id}');
              continue;
            }

            // Create the journal entry
            await _journalEntryService.createJournalEntry(journalEntry);
            log('✅ Created journal entry for payment ${payment.id}');

            // Update account balances
            await _updateAccountBalancesForPaymentJournalEntry(
                journalEntry, uid);

            // Create ledger entries
            final ledgerService = AccountLedgerService();
            final ledgerSuccess = await ledgerService
                .createLedgerEntriesFromJournalEntry(journalEntry);

            if (!ledgerSuccess) {
              log('❌ Failed to create ledger entries for payment journal entry: ${journalEntry.entryNumber}');
            } else {
              log('✅ Created ledger entries for payment: ${payment.id}');
            }

            journalEntriesCreated++;
          }

          log('✅ Successfully processed new payment ${payment.id}');
        } catch (e) {
          log('❌ Error processing payment ${payment.id}: $e');
          // Continue with other payments instead of failing completely
          continue;
        }
      }

      log('✅ Completed processing new payment transactions for voucher: ${newVoucher.voucherNumber}');
      log('📊 Payment journal entry summary:');
      log('   Total new payments processed: ${newPayments.length}');
      log('   Journal entries created: $journalEntriesCreated');
      log('   Payments skipped (no account): ${newPayments.length - journalEntriesCreated}');
    } catch (e) {
      log('❌ Error in _processNewPaymentTransactionsOnly: $e');
      rethrow;
    }
  }

  /// Batch process existing vouchers to create journal entries
  Future<BatchProcessResult> processExistingVouchers(
      List<VoucherModel> vouchers, String uid) async {
    try {
      log('Processing ${vouchers.length} existing vouchers for journal entries');
      return await _integrationService.batchProcessVoucherTransactions(
          vouchers, uid);
    } catch (e) {
      log('Error processing existing vouchers: $e');
      return BatchProcessResult(
        totalProcessed: vouchers.length,
        successCount: 0,
        failureCount: vouchers.length,
        failedTransactionIds: vouchers.map((v) => v.voucherNumber).toList(),
      );
    }
  }

  /// Get journal entries for a specific voucher
  Future<List<dynamic>> getVoucherJournalEntries(
      String voucherNumber, String uid) async {
    return await _integrationService.getJournalEntriesForVoucher(
        voucherNumber, uid);
  }

  /// Get financial summary for a voucher
  Future<VoucherFinancialSummary> getVoucherFinancialSummary(
      VoucherModel voucher) async {
    return await _integrationService.getVoucherFinancialSummary(voucher);
  }

  /// Check if accounting integration is properly configured
  Future<bool> isAccountingConfigured(String uid) async {
    try {
      final mapping = await _mappingService.getVoucherAccountMapping(uid);
      return mapping != null;
    } catch (e) {
      log('Error checking accounting configuration: $e');
      return false;
    }
  }

  /// Get configuration status for UI display
  Future<AccountingConfigurationStatus> getConfigurationStatus(
      String uid) async {
    try {
      final mapping = await _mappingService.getVoucherAccountMapping(uid);

      if (mapping == null) {
        return AccountingConfigurationStatus(
          isConfigured: false,
          missingAccounts: [
            'Revenue Account',
            'Broker Expense Account',
            'Munshiana Expense Account'
          ],
          message:
              'Required accounts for voucher journal entries are not configured',
        );
      }

      return AccountingConfigurationStatus(
        isConfigured: true,
        missingAccounts: [],
        message: 'Accounting integration is properly configured',
      );
    } catch (e) {
      return AccountingConfigurationStatus(
        isConfigured: false,
        missingAccounts: [],
        message: 'Error checking configuration: $e',
      );
    }
  }

  /// Process voucher and payment transactions sequentially with proper numbering
  Future<void> _processVoucherAndPaymentTransactionsSequentially(
      VoucherModel voucher, String uid) async {
    try {
      log('🔄 Processing voucher and payment transactions sequentially for: ${voucher.voucherNumber}');

      // Step 1: Process the main voucher transaction first
      log('📝 Step 1: Creating main voucher journal entry...');
      final voucherSuccess =
          await _integrationService.processVoucherTransaction(voucher, uid);
      if (!voucherSuccess) {
        log('❌ Failed to create main voucher journal entry for: ${voucher.voucherNumber}');
        throw Exception(
            'Failed to create main voucher journal entry for: ${voucher.voucherNumber}');
      }
      log('✅ Main voucher journal entry created successfully');

      // Step 2: Process payment transactions immediately after
      log('💳 Step 2: Creating payment journal entries...');
      await _processPaymentTransactionsWithSequentialNumbering(voucher, uid);
      log('✅ All journal entries created with proper sequential numbering');
    } catch (e) {
      log('❌ Error in sequential transaction processing: $e');
      rethrow;
    }
  }

  /// Process payment transactions and generate journal entries with sequential numbering
  Future<void> _processPaymentTransactionsWithSequentialNumbering(
      VoucherModel voucher, String uid) async {
    try {
      log('💳 Processing ${voucher.paymentTransactions.length} payment transactions for voucher: ${voucher.voucherNumber}');

      // Convert payment transaction maps to PaymentTransactionModel objects
      final paymentModels = <PaymentTransactionModel>[];
      for (final paymentMap in voucher.paymentTransactions) {
        try {
          final payment = PaymentTransactionModel.fromMap(paymentMap);
          paymentModels.add(payment);
          log('✅ Converted payment transaction: ${payment.id} (${payment.method.name})');
        } catch (e) {
          log('❌ Failed to convert payment transaction: $e');
          log('📋 Payment data: $paymentMap');
          // Continue with other payments instead of failing completely
          continue;
        }
      }

      if (paymentModels.isEmpty) {
        log('ℹ️ No valid payment transactions found for voucher: ${voucher.voucherNumber}');
        return;
      }

      log('📊 Payment processing summary for voucher ${voucher.voucherNumber}:');
      log('   Total payments: ${paymentModels.length}');
      final paymentsWithAccounts = paymentModels
          .where((p) => p.accountId != null && p.accountId!.isNotEmpty)
          .length;
      final paymentsWithoutAccounts =
          paymentModels.length - paymentsWithAccounts;
      log('   Payments with Chart of Accounts: $paymentsWithAccounts');
      log('   Payments WITHOUT Chart of Accounts: $paymentsWithoutAccounts');

      if (paymentsWithoutAccounts > 0) {
        log('⚠️ WARNING: $paymentsWithoutAccounts payments will be skipped for journal entry generation');
        for (final payment in paymentModels) {
          if (payment.accountId == null || payment.accountId!.isEmpty) {
            log('   - ${payment.method.name}: ${payment.amount} (ID: ${payment.id})');
          }
        }
      }

      log('🗓️ Processing payment transactions with their individual transaction dates');

      // Process each payment transaction individually with sequential numbering
      int journalEntriesCreated = 0;
      for (final payment in paymentModels) {
        try {
          log('🔄 Processing payment: ${payment.id} (${payment.method.name}, Amount: ${payment.amount})');
          log('🔍 Payment details - AccountId: ${payment.accountId}, AccountName: ${payment.accountName}');
          log('🗓️ Payment transaction date: ${payment.transactionDate}');

          // Assign default Chart of Accounts for payments without account reference
          PaymentTransactionModel processedPayment = payment;
          if (payment.accountId == null || payment.accountId!.isEmpty) {
            log('🔧 Payment ${payment.id} missing Chart of Accounts - attempting to assign default account');
            processedPayment = await _assignDefaultAccountToPayment(payment);

            if (processedPayment.accountId == null ||
                processedPayment.accountId!.isEmpty) {
              log('⚠️ SKIPPING payment ${payment.id} - could not assign default Chart of Accounts');
              log('⚠️ Payment method: ${payment.method.name}, Amount: ${payment.amount}');
              log('⚠️ This payment will NOT generate a journal entry');
              continue;
            } else {
              log('✅ Assigned default account to payment ${payment.id}: ${processedPayment.accountName}');
            }
          }

          // Preserve the user-selected payment transaction date instead of overriding with voucher date
          log('✅ Preserving user-selected payment transaction date: ${processedPayment.transactionDate}');

          // Handle check usage tracking for check payments
          if (processedPayment.method == PaymentMethod.check &&
              processedPayment.checkNumber != null &&
              processedPayment.checkNumber!.isNotEmpty) {
            await _createCheckUsageRecord(processedPayment, voucher);
          }

          // Check if this payment should use loan-based workflow
          final shouldUseLoanWorkflow = _loanIntegrationService
              .shouldUseLoanBasedWorkflow(processedPayment.method);

          log('🔍 DEBUG: Payment method: ${processedPayment.method}');
          log('🔍 DEBUG: Should use loan workflow: $shouldUseLoanWorkflow');

          if (shouldUseLoanWorkflow) {
            // Check if this is an "Other" payment type based on account ownership
            final isOtherPaymentType =
                await _isOtherPaymentType(processedPayment);

            log('🔍 DEBUG: Is Other payment type (cross-company): $isOtherPaymentType');

            // Create pending loan requests for "Other" payment types (cross-company)
            if (isOtherPaymentType) {
              log('🔄 Using pending loan request workflow for "Other" payment: ${payment.id}');

              // Create pending loan request (requires approval from other company)
              final loanRequestResult =
                  await _loanIntegrationService.createLoanRequestFromPayment(
                payment: processedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (loanRequestResult.isLeft) {
                log('❌ Failed to create loan request for payment: ${payment.id}');
                continue;
              }

              final updatedPayment = loanRequestResult.right;
              log('✅ Created pending loan request for payment: ${payment.id}');

              // Create auto-posted payment journal entry (will be completed upon loan approval)
              final pendingJournalResult = await _loanIntegrationService
                  .createAutoPostedPaymentJournalEntry(
                payment: updatedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (pendingJournalResult.isLeft) {
                log('❌ Failed to create pending journal entry for payment: ${payment.id}');
                // Note: Loan request is already created, so we don't rollback here
                // The loan request will exist but without journal entry
                continue;
              }

              log('✅ Created pending journal entry for payment: ${payment.id}');
              log('🎯 Pending loan request ${updatedPayment.loanRequestId} should appear in Loan Requests and require approval from other company');
            } else {
              // "Own" payment type - use traditional workflow instead of loan workflow
              log('🔄 Own payment type detected - using traditional workflow for payment: ${payment.id}');
              log('✅ Skipping loan request creation for same-company payment');

              // Traditional workflow: Generate and create journal entry immediately
              final journalEntry =
                  await _automaticJournalService.generatePaymentJournalEntry(
                payment: processedPayment,
                uid: uid,
                createdBy: 'system',
              );

              if (journalEntry == null) {
                log('❌ Failed to generate journal entry for Own payment: ${payment.id}');
                continue;
              }

              // Create the journal entry in Firebase
              try {
                await _journalEntryService.createJournalEntry(journalEntry);
                log('✅ Created traditional journal entry for Own payment: ${payment.id}');
                log('📊 Journal entry details: ${journalEntry.lines.length} lines, Total Debits: ${journalEntry.totalDebits}, Total Credits: ${journalEntry.totalCredits}');
              } catch (e) {
                log('❌ Failed to save journal entry for Own payment: ${payment.id} - $e');
                continue;
              }
            }

            // Note: Journal entry is posted immediately, loan approval will create additional disbursement entries
            journalEntriesCreated++;
          }
        } catch (e) {
          log('❌ Error processing payment ${payment.id}: $e');
          // Continue with other payments instead of failing completely
          continue;
        }
      }

      log('✅ Completed processing payment transactions for voucher: ${voucher.voucherNumber}');
      log('📊 Payment journal entry summary:');
      log('   Total payments processed: ${paymentModels.length}');
      log('   Journal entries created: $journalEntriesCreated');
      log('   Payments skipped (no account): ${paymentModels.length - journalEntriesCreated}');
    } catch (e) {
      log('❌ Error in _processPaymentTransactionsWithSequentialNumbering: $e');
      // Don't throw - payment journal entries are supplementary to main voucher journal entries
    }
  }

  /// Update account balances for payment journal entry lines
  Future<void> _updateAccountBalancesForPaymentJournalEntry(
      JournalEntryModel journalEntry, String uid) async {
    try {
      log('💰 Updating account balances for payment journal entry: ${journalEntry.entryNumber}');

      for (final line in journalEntry.lines) {
        try {
          // Get the account
          final account =
              await _chartOfAccountsService.getAccountById(line.accountId);
          if (account == null) {
            log('❌ Account not found for balance update: ${line.accountId}');
            continue;
          }

          // Calculate balance change based on account type and debit/credit
          final balanceChange = AccountTypeHelperService.calculateBalanceChange(
            accountType: account.accountType,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
          );

          // Update the account balance
          final updatedAccount = account.copyWith(
            balance: account.balance + balanceChange,
            updatedAt: DateTime.now(),
          );

          await _chartOfAccountsService.updateAccount(updatedAccount);
          log('✅ Updated balance for account ${account.accountName}: ${account.balance} → ${updatedAccount.balance}');
        } catch (e) {
          log('❌ Error updating balance for account ${line.accountId}: $e');
          // Continue with other accounts
        }
      }
    } catch (e) {
      log('❌ Error in _updateAccountBalancesForPaymentJournalEntry: $e');
    }
  }

  /// Assign default Chart of Accounts to payment transaction based on payment method
  Future<PaymentTransactionModel> _assignDefaultAccountToPayment(
      PaymentTransactionModel payment) async {
    try {
      log('🔧 Assigning default account for payment method: ${payment.method.name}');

      ChartOfAccountsModel? defaultAccount;

      // Get appropriate default account based on payment method
      switch (payment.method) {
        case PaymentMethod.cash:
          // Look for a cash account
          defaultAccount = await _findAccountByType(AccountType.cash);
          break;
        case PaymentMethod.fuelCard:
          // Look for a fuel expense or cash account
          defaultAccount = await _findAccountByType(AccountType.cash) ??
              await _findAccountByType(AccountType.currentAssets);
          break;
        case PaymentMethod.check:
        case PaymentMethod.accountTransfer:
          // Look for a bank account
          defaultAccount = await _findAccountByType(AccountType.bank);
          break;
      }

      if (defaultAccount != null) {
        log('✅ Found default account for ${payment.method.name}: ${defaultAccount.accountName}');
        return payment.copyWith(
          accountId: defaultAccount.id,
          accountName: defaultAccount.accountName,
        );
      } else {
        log('❌ No default account found for payment method: ${payment.method.name}');
        return payment;
      }
    } catch (e) {
      log('❌ Error assigning default account to payment: $e');
      return payment;
    }
  }

  /// Find Chart of Accounts by account type
  Future<ChartOfAccountsModel?> _findAccountByType(
      AccountType accountType) async {
    try {
      final accounts =
          await _chartOfAccountsService.getAccountsByType(accountType);
      if (accounts.isNotEmpty) {
        // Return the first active account of this type
        final activeAccount = accounts.firstWhere(
          (account) => account.isActive,
          orElse: () => accounts.first,
        );
        return activeAccount;
      }
      return null;
    } catch (e) {
      log('❌ Error finding account by type $accountType: $e');
      return null;
    }
  }

  /// Determine if a payment is cross-company ("Other") or same-company ("Own")
  /// based on account ownership rather than payment method
  Future<bool> _isOtherPaymentType(PaymentTransactionModel payment) async {
    try {
      // If no account is selected, default to "Own" payment type
      if (payment.accountId == null || payment.accountId!.isEmpty) {
        log('🔍 No account selected, defaulting to Own payment type');
        return false;
      }

      // Get account details to check ownership
      final account = await _chartOfAccountsService
          .getAccountByIdCrossCompany(payment.accountId!);

      if (account == null) {
        log('❌ Account not found: ${payment.accountId}, defaulting to Own payment type');
        return false;
      }

      // Get current user UID for comparison
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        log('❌ No current user, defaulting to Own payment type');
        return false;
      }

      // If account belongs to different company, it's "Other" payment type
      final isOtherPayment = account.uid != currentUser.uid;

      log('🔍 Payment type determination:');
      log('   - Account: ${account.accountName} (${account.accountNumber})');
      log('   - Account UID: ${account.uid}');
      log('   - Current User UID: ${currentUser.uid}');
      log('   - Is Other Payment: $isOtherPayment');

      return isOtherPayment;
    } catch (e) {
      log('❌ Error determining payment type: $e, defaulting to Own payment type');
      return false;
    }
  }

  /// Create check usage record for tracking check payments
  Future<void> _createCheckUsageRecord(
      PaymentTransactionModel paymentTransaction, VoucherModel voucher) async {
    try {
      log('Creating check usage record for voucher: ${voucher.voucherNumber}');
      log('🔍 DEBUG: Check payment amount: ${paymentTransaction.amount}');
      log('🔍 DEBUG: Total voucher freight: ${voucher.totalFreight}');
      log('🔍 DEBUG: Check number: ${paymentTransaction.checkNumber}');

      // Determine check type and company info
      String checkType = 'own'; // Default to own company
      String? externalCompanyName;
      String? externalCompanyId;

      // Check if this is a cross-company payment by examining account ownership
      if (paymentTransaction.accountId != null &&
          paymentTransaction.accountId!.isNotEmpty) {
        try {
          // Use cross-company method to get account details regardless of ownership
          final account = await _chartOfAccountsService
              .getAccountByIdCrossCompany(paymentTransaction.accountId!);

          if (account != null) {
            // Get current user UID for comparison
            final currentUser = FirebaseAuth.instance.currentUser;
            if (currentUser != null && account.uid != currentUser.uid) {
              checkType = 'other';
              externalCompanyId = account.uid;

              // Get the proper company name from the user database
              try {
                final companyService = CompanyFirebaseService();
                final userResult =
                    await companyService.getUserById(account.uid);

                userResult.fold(
                  (failure) {
                    // Fallback to account name if company lookup fails
                    externalCompanyName = account.accountName;
                    log('Could not get company name for UID ${account.uid}, using account name: $externalCompanyName');
                  },
                  (userModel) {
                    externalCompanyName = userModel.companyName;
                    log('Retrieved external company name: $externalCompanyName');
                  },
                );
              } catch (e) {
                // Fallback to account name if company service fails
                externalCompanyName = account.accountName;
                log('Error getting company name, using account name: $e');
              }

              log('Detected cross-company check payment to company: $externalCompanyName (UID: $externalCompanyId)');
            } else {
              log('Check payment is from own company account: ${account.accountName}');
            }
          } else {
            log('Account not found for check usage tracking: ${paymentTransaction.accountId}');
          }
        } catch (e) {
          log('Error determining check type: $e');
        }
      }

      log('✅ Creating check usage record with amount: ${paymentTransaction.amount}');

      final checkUsageRecord = CheckUsageModel(
        id: const Uuid().v4(),
        uid: '', // Will be set by the service
        checkNumber: paymentTransaction.checkNumber!,
        bankName: paymentTransaction.bankName ?? '',
        accountId: paymentTransaction.accountId ?? '',
        accountName: paymentTransaction.accountName ?? '',
        voucherId: voucher.voucherNumber,
        voucherNumber: voucher.voucherNumber,
        amount: paymentTransaction.amount,
        issueDate: paymentTransaction.checkIssueDate ??
            paymentTransaction.transactionDate,
        expiryDate: paymentTransaction.checkExpiryDate,
        usageDate: paymentTransaction.transactionDate,
        payeeName: voucher.driverName.isNotEmpty
            ? voucher.driverName
            : 'Driver Payment',
        notes: paymentTransaction.notes ??
            'Check payment for voucher ${voucher.voucherNumber}',
        status: 'issued',
        checkType: checkType,
        externalCompanyId: externalCompanyId,
        externalCompanyName: externalCompanyName,
      );

      final checkUsageRepository =
          CheckUsageRepository(CheckUsageFirebaseService());
      final result =
          await checkUsageRepository.createCheckUsage(checkUsageRecord);

      result.fold(
        (failure) => {
          log('Failed to create check usage record: $failure'),
        },
        (_) => {
          log('Successfully created check usage record for check: ${paymentTransaction.checkNumber}'),
        },
      );
    } catch (e) {
      log('Error creating check usage record: $e');
      // Don't throw error to avoid breaking the main voucher workflow
    }
  }

  /// Record broker fee transaction in the broker financial tracking system
  Future<void> _recordBrokerFeeTransaction(
      VoucherModel voucher, String uid) async {
    try {
      log('🔍 VoucherAccountingHookService: Checking broker fee transaction for voucher: ${voucher.voucherNumber}');
      log('🔍 Broker fees: ${voucher.brokerFees}');
      log('🔍 Selected broker: "${voucher.selectedBroker}"');
      log('🔍 Broker name: "${voucher.brokerName}"');

      // Check if broker fees exist and broker is selected
      if (voucher.brokerFees <= 0 || voucher.selectedBroker.isEmpty) {
        log('🔍 VoucherAccountingHookService: No broker fees to record for voucher: ${voucher.voucherNumber}');
        log('🔍 Reason: brokerFees=${voucher.brokerFees}, selectedBroker="${voucher.selectedBroker}"');
        return;
      }

      // Check if broker financial service is available
      if (_brokerFinancialService == null) {
        log('❌ VoucherAccountingHookService: BrokerFinancialService not available - trying to initialize...');
        try {
          _brokerFinancialService = Get.find<BrokerFinancialService>();
          log('✅ VoucherAccountingHookService: BrokerFinancialService initialized successfully');
        } catch (e) {
          log('❌ VoucherAccountingHookService: Failed to initialize BrokerFinancialService: $e');
          return;
        }
      }

      log('💰 VoucherAccountingHookService: Recording broker fee transaction for voucher: ${voucher.voucherNumber}');
      log('💰 Broker: ${voucher.selectedBroker}, Amount: ${voucher.brokerFees}');

      // Get the broker ID from the selected broker name
      // Note: This assumes selectedBroker contains the broker ID or name
      final brokerId = voucher.selectedBroker;
      final brokerName = voucher.brokerName.isNotEmpty
          ? voucher.brokerName
          : voucher.selectedBroker;

      // Parse departure date
      DateTime transactionDate;
      try {
        transactionDate = DateTime.parse(voucher.departureDate);
      } catch (e) {
        log('Warning: Failed to parse departure date, using current date: $e');
        transactionDate = DateTime.now();
      }

      // Record the broker fee transaction
      final result = await _brokerFinancialService!.recordBrokerFee(
        brokerId: brokerId,
        brokerName: brokerName,
        amount: voucher.brokerFees,
        transactionDate: transactionDate,
        voucherId: voucher.voucherNumber, // Use voucher number as ID
        voucherNumber: voucher.voucherNumber,
      );

      result.fold(
        (failure) {
          log('❌ VoucherAccountingHookService: Failed to record broker fee transaction: ${failure.message}');
          // Don't throw error to avoid breaking the main voucher workflow
        },
        (_) {
          log('✅ VoucherAccountingHookService: Successfully recorded broker fee transaction for voucher: ${voucher.voucherNumber}');
        },
      );
    } catch (e) {
      log('❌ VoucherAccountingHookService: Error recording broker fee transaction: $e');
      // Don't throw error to avoid breaking the main voucher workflow
    }
  }
}

/// Configuration status for accounting integration
class AccountingConfigurationStatus {
  final bool isConfigured;
  final List<String> missingAccounts;
  final String message;

  AccountingConfigurationStatus({
    required this.isConfigured,
    required this.missingAccounts,
    required this.message,
  });
}
