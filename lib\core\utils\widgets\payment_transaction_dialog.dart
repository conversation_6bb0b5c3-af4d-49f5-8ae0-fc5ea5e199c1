import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/fuel_card_model.dart';
import 'package:logestics/models/payment_transaction_model.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/firebase_service/finance/company_firebase_service.dart';

import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

// Define our payment source types for a clearer separation
enum PaymentSourceType { account, fuelCard }

class PaymentTransactionDialog extends StatefulWidget {
  final String voucherId;
  final double totalFreight;
  final double settledFreight;
  final List<AccountModel> accounts;
  final List<FuelCardModel> fuelCards;
  final PaymentTransactionModel?
      transaction; // For editing existing transaction
  final Function(PaymentTransactionModel) onSave;
  final bool allowCashPayments; // NEW: Control cash payment availability
  final DateTime?
      defaultTransactionDate; // Default transaction date (usually voucher departure date)

  const PaymentTransactionDialog({
    super.key,
    required this.voucherId,
    required this.totalFreight,
    required this.settledFreight,
    required this.accounts,
    required this.fuelCards,
    required this.onSave,
    this.transaction,
    this.allowCashPayments = true, // Default to true for backward compatibility
    this.defaultTransactionDate,
  });

  @override
  State<PaymentTransactionDialog> createState() =>
      _PaymentTransactionDialogState();
}

class _PaymentTransactionDialogState extends State<PaymentTransactionDialog> {
  // New field to track payment source type
  late PaymentSourceType selectedSourceType;
  late PaymentMethod selectedMethod;
  late PaymentStatus selectedStatus;

  // Controllers
  final amountController = TextEditingController();
  final notesController = TextEditingController();
  final checkNumberController = TextEditingController();
  final bankNameController = TextEditingController();
  final fuelLitersController = TextEditingController();

  // Date fields for checks
  DateTime? selectedCheckIssueDate;
  DateTime? selectedCheckExpiryDate;

  // Transaction date field (for all payment methods)
  late DateTime selectedTransactionDate;

  // Cheque type selection
  String selectedChequeType = 'Own'; // 'Own' or 'Other'

  // Company selection for 'Other' cheque type
  UserModel? selectedCompany;
  List<UserModel> companies = [];
  List<AccountModel> externalCompanyAccounts = [];

  // Chart of Accounts for external company selection
  List<ChartOfAccountsModel> externalCompanyChartAccounts = [];

  // Cheque expiry date
  DateTime? selectedChequeExpiryDate;

  // For dropdown selections
  AccountModel? selectedAccount;
  String? selectedFuelCompany;
  FuelCardModel? selectedFuelCard;
  String? selectedFuelStation;

  // Chart of Accounts selection for modern account integration
  ChartOfAccountsModel? selectedChartOfAccountsAccount;

  // File attachment
  String? attachmentUrl;

  // Form key
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    if (widget.transaction != null) {
      // Initialize with existing transaction data for editing
      selectedMethod = widget.transaction!.method;
      selectedStatus = widget.transaction!.status;
      amountController.text = widget.transaction!.amount.toString();
      notesController.text = widget.transaction!.notes ?? '';

      // Set the source type based on method
      selectedSourceType = widget.transaction!.method == PaymentMethod.fuelCard
          ? PaymentSourceType.fuelCard
          : PaymentSourceType.account;

      // Method-specific fields
      if (widget.transaction!.method == PaymentMethod.check) {
        checkNumberController.text = widget.transaction!.checkNumber ?? '';
        bankNameController.text = widget.transaction!.bankName ?? '';
        selectedCheckIssueDate = widget.transaction!.checkIssueDate;
        selectedCheckExpiryDate = widget.transaction!.checkExpiryDate;
      } else if (widget.transaction!.method == PaymentMethod.accountTransfer) {
        try {
          selectedAccount = widget.accounts.firstWhere(
            (acc) => acc.id == widget.transaction!.accountId,
          );
        } catch (e) {
          selectedAccount = null;
        }
      } else if (widget.transaction!.method == PaymentMethod.fuelCard) {
        selectedFuelCompany = widget.transaction!.fuelCompany;
        fuelLitersController.text =
            widget.transaction!.fuelLiters?.toString() ?? '';

        // Find matching fuel card
        if (widget.transaction!.fuelCardId != null) {
          try {
            selectedFuelCard = widget.fuelCards.firstWhere(
              (card) => card.id == widget.transaction!.fuelCardId,
            );

            // Set the fuel station based on the selected card
            if (selectedFuelCard != null) {
              selectedFuelStation = selectedFuelCard!.fuelStationName;
            }
          } catch (e) {
            selectedFuelCard = null;
          }
        }
      }

      // Initialize transaction date for editing
      selectedTransactionDate = widget.transaction!.transactionDate;
      attachmentUrl = widget.transaction!.attachmentUrl;
    } else {
      // Initialize with defaults for new transaction
      selectedSourceType = PaymentSourceType.account;

      // FIXED: Set default method based on allowCashPayments
      selectedMethod = widget.allowCashPayments
          ? PaymentMethod.check
          : PaymentMethod.check; // Default to check if cash not allowed

      selectedStatus = PaymentStatus.paid;

      // Set default amount to pending amount
      final pendingAmount = widget.totalFreight - widget.settledFreight;
      if (pendingAmount > 0) {
        amountController.text = pendingAmount.toString();
        log('🔍 DEBUG: PaymentDialog - Set default amount to pending: $pendingAmount');
        log('🔍 DEBUG: PaymentDialog - Total freight: ${widget.totalFreight}, Settled: ${widget.settledFreight}');
      }

      // Initialize transaction date with default or current date
      selectedTransactionDate = widget.defaultTransactionDate ?? DateTime.now();
    }

    // Load companies for cross-company cheque transactions
    _loadCompanies();
  }

  // Load companies for 'Other' cheque type selection
  Future<void> _loadCompanies() async {
    try {
      final companyService = CompanyFirebaseService();
      final result = await companyService.getAllUsers();
      result.fold(
        (failure) {
          // Handle error silently for now
          log('Failed to load companies: ${failure.message}');
        },
        (loadedCompanies) {
          setState(() {
            companies = loadedCompanies;
          });
        },
      );
    } catch (e) {
      log('Error loading companies: $e');
    }
  }

  // Load Chart of Accounts for selected external company
  Future<void> _loadExternalCompanyChartAccounts(String companyId) async {
    try {
      log('Loading Chart of Accounts for company: $companyId');

      // Query Chart of Accounts directly for the external company
      final snapshot = await FirebaseFirestore.instance
          .collection('chart_of_accounts')
          .where('uid', isEqualTo: companyId)
          .where('isActive', isEqualTo: true)
          .where('category', isEqualTo: AccountCategory.assets.name)
          .get();

      final allAccounts = snapshot.docs
          .map((doc) {
            try {
              return ChartOfAccountsModel.fromJson(doc.data());
            } catch (e) {
              log('Error parsing Chart of Accounts document ${doc.id}: $e');
              return null;
            }
          })
          .where((account) => account != null)
          .cast<ChartOfAccountsModel>()
          .toList();

      // Filter to only specific Asset account types for payment selection
      final assetAccounts = allAccounts.where((account) {
        return account.accountType == AccountType.cash ||
            account.accountType == AccountType.bank ||
            account.accountType == AccountType.currentAssets;
      }).toList();

      setState(() {
        externalCompanyChartAccounts = assetAccounts;
      });

      log('Successfully loaded ${assetAccounts.length} Chart of Accounts asset accounts for company: $companyId');
    } catch (e) {
      log('Error loading external company Chart of Accounts: $e');
      setState(() {
        externalCompanyChartAccounts = [];
      });
    }
  }

  @override
  void dispose() {
    amountController.dispose();
    notesController.dispose();
    checkNumberController.dispose();
    bankNameController.dispose();
    fuelLitersController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Dialog title
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.transaction != null
                          ? 'Edit Payment Transaction'
                          : 'Add Payment Transaction',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Freight Summary
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Freight Summary',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Total Freight:'),
                          Text(
                            'PKR ${widget.totalFreight.toStringAsFixed(2)}',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Already Paid:'),
                          Text(
                            'PKR ${widget.settledFreight.toStringAsFixed(2)}',
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Remaining Amount:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'PKR ${(widget.totalFreight - widget.settledFreight).toStringAsFixed(2)}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Payment source type selection
                const Text(
                  'Payment Source Type',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 10),

                // Source type radio buttons
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<PaymentSourceType>(
                        title: const Text('Account'),
                        value: PaymentSourceType.account,
                        groupValue: selectedSourceType,
                        onChanged: (value) {
                          setState(() {
                            selectedSourceType = value!;
                            // Default to check when switching to account (removed cash)
                            selectedMethod = PaymentMethod.check;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<PaymentSourceType>(
                        title: const Text('Fuel Card'),
                        value: PaymentSourceType.fuelCard,
                        groupValue: selectedSourceType,
                        onChanged: (value) {
                          setState(() {
                            selectedSourceType = value!;
                            selectedMethod = PaymentMethod.fuelCard;
                          });
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Source-specific fields
                if (selectedSourceType == PaymentSourceType.account)
                  _buildAccountFields(),
                if (selectedSourceType == PaymentSourceType.fuelCard)
                  _buildFuelCardFields(),

                const SizedBox(height: 20),

                // Common fields
                TextFormField(
                  controller: amountController,
                  decoration: const InputDecoration(
                    labelText: 'Amount',
                    hintText: 'Enter payment amount',
                    border: OutlineInputBorder(),
                    prefixText: 'PKR ',
                  ),
                  enabled: selectedMethod !=
                      PaymentMethod
                          .fuelCard, // Disable for fuel card as it's calculated
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      // Trigger rebuild to update status display
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Amount is required';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null) {
                      return 'Please enter a valid amount';
                    }
                    if (amount <= 0) {
                      return 'Amount must be greater than 0';
                    }

                    // Check if amount exceeds remaining freight
                    final remainingFreight =
                        widget.totalFreight - widget.settledFreight;
                    if (amount > remainingFreight) {
                      return 'Amount cannot exceed remaining freight (PKR ${remainingFreight.toStringAsFixed(2)})';
                    }

                    // Check if total would exceed total freight
                    if (widget.settledFreight + amount > widget.totalFreight) {
                      return 'Total payments cannot exceed total freight (PKR ${widget.totalFreight.toStringAsFixed(2)})';
                    }

                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Status field - Auto-calculated based on amount
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Payment Status',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _getCalculatedStatus(),
                        style: TextStyle(
                          fontSize: 14,
                          color: _getStatusColor(),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusDescription(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Notes field
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    hintText: 'Enter any additional notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                const SizedBox(height: 16),

                // Transaction Date field
                InkWell(
                  onTap: () => _selectTransactionDate(context),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Transaction Date *',
                      hintText: 'Select transaction date',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      _formatDate(selectedTransactionDate),
                      style: const TextStyle(
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomButton.danger(
                      text: 'Cancel',
                      onPressed: () => Navigator.pop(context),
                      minimumSize: const Size(100, 40),
                    ),
                    const SizedBox(width: 16),
                    CustomButton.primary(
                      text: 'Save',
                      onPressed: _saveTransaction,
                      minimumSize: const Size(100, 40),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAccountFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Account Payment Method',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 10),

        // Payment method selection - UPDATED to conditionally show cash
        Row(
          children: [
            // Only show cash option if allowCashPayments is true
            if (widget.allowCashPayments)
              Expanded(
                child: RadioListTile<PaymentMethod>(
                  title: const Text('Cash'),
                  value: PaymentMethod.cash,
                  groupValue: selectedMethod,
                  onChanged: (value) {
                    setState(() {
                      selectedMethod = value!;
                    });
                  },
                ),
              ),
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('Check'),
                value: PaymentMethod.check,
                groupValue: selectedMethod,
                onChanged: (value) {
                  setState(() {
                    selectedMethod = value!;
                  });
                },
              ),
            ),
            // Add spacing if cash is not shown to keep layout balanced
            if (!widget.allowCashPayments) const Spacer(),
          ],
        ),

        Row(
          children: [
            Expanded(
              child: RadioListTile<PaymentMethod>(
                title: const Text('Account Transfer'),
                value: PaymentMethod.accountTransfer,
                groupValue: selectedMethod,
                onChanged: (value) {
                  setState(() {
                    selectedMethod = value!;
                  });
                },
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Check payment specific fields
        if (selectedMethod == PaymentMethod.check) ...[
          TextFormField(
            controller: checkNumberController,
            decoration: const InputDecoration(
              labelText: 'Check Number',
              hintText: 'Enter check number',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (selectedMethod == PaymentMethod.check &&
                  (value == null || value.isEmpty)) {
                return 'Check number is required';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Cheque Type selector
          const Text(
            'Cheque Type',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Own'),
                  value: 'Own',
                  groupValue: selectedChequeType,
                  onChanged: (value) {
                    setState(() {
                      selectedChequeType = value!;
                      selectedAccount = null; // Reset account selection
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Other'),
                  value: 'Other',
                  groupValue: selectedChequeType,
                  onChanged: (value) {
                    setState(() {
                      selectedChequeType = value!;
                      selectedAccount = null; // Reset account selection
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Bank account selection - different based on cheque type
          if (selectedChequeType == 'Own') ...[
            // Own company accounts using Chart of Accounts
            AssetAccountDropdown(
              labelText: 'Select Bank Account',
              hintText: 'Choose the bank account for this check',
              selectedAccount: selectedChartOfAccountsAccount,
              onChanged: (account) {
                setState(() {
                  selectedChartOfAccountsAccount = account;
                  // Update legacy fields for backward compatibility
                  selectedAccount = account != null
                      ? AccountModel(
                          id: account.id,
                          name: account.accountName,
                          accountNumber: account.accountNumber,
                          initialBalance: account.balance,
                          availableBalance: account.balance,
                          branchCode: '',
                          branchAddress: '',
                          createdAt: account.createdAt,
                          uid: account.uid,
                        )
                      : null;
                  bankNameController.text = account?.accountName ?? '';
                });
              },
              isRequired: true,
              validator: (value) {
                if (selectedMethod == PaymentMethod.check && value == null) {
                  return 'Please select a bank account';
                }
                return null;
              },
            ),
          ] else ...[
            // Other company selection
            DropdownButtonFormField<UserModel>(
              decoration: const InputDecoration(
                labelText: 'Select Company',
                hintText: 'Choose the company for this check',
                border: OutlineInputBorder(),
              ),
              value: selectedCompany,
              items: companies.map((company) {
                return DropdownMenuItem<UserModel>(
                  value: company,
                  child: Text(company.companyName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedCompany = value;
                  selectedAccount = null; // Reset account selection
                  selectedChartOfAccountsAccount =
                      null; // Reset Chart of Accounts selection
                  externalCompanyAccounts = []; // Clear external accounts
                  externalCompanyChartAccounts =
                      []; // Clear external Chart of Accounts
                });

                // Load Chart of Accounts for the selected company
                if (value != null) {
                  _loadExternalCompanyChartAccounts(value.uid);
                }
              },
              validator: (value) {
                if (selectedMethod == PaymentMethod.check &&
                    selectedChequeType == 'Other' &&
                    value == null) {
                  return 'Please select a company';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // External company Chart of Accounts selection
            if (selectedCompany != null) ...[
              if (externalCompanyChartAccounts.isEmpty) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      const CircularProgressIndicator(strokeWidth: 2),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Loading bank accounts for ${selectedCompany!.companyName}...',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                DropdownButtonFormField<ChartOfAccountsModel>(
                  decoration: const InputDecoration(
                    labelText: 'Select External Bank Account',
                    hintText: 'Choose bank account from external company',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.account_balance),
                  ),
                  value: selectedChartOfAccountsAccount,
                  items: externalCompanyChartAccounts.map((account) {
                    return DropdownMenuItem<ChartOfAccountsModel>(
                      value: account,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            account.accountName,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            '${account.accountNumber} • ${account.accountType.displayName}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedChartOfAccountsAccount = value;
                      // Update legacy fields for backward compatibility
                      selectedAccount = value != null
                          ? AccountModel(
                              id: value.id,
                              name: value.accountName,
                              accountNumber: value.accountNumber,
                              initialBalance: value.balance,
                              availableBalance: value.balance,
                              branchCode: '',
                              branchAddress: '',
                              createdAt: value.createdAt,
                              uid: value.uid,
                            )
                          : null;
                      bankNameController.text = value?.accountName ?? '';
                    });
                  },
                  validator: (value) {
                    if (selectedMethod == PaymentMethod.check &&
                        selectedChequeType == 'Other' &&
                        value == null) {
                      return 'Please select a bank account';
                    }
                    return null;
                  },
                  isExpanded: true,
                  menuMaxHeight: 300,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline,
                          color: Colors.amber.shade700, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'This will deduct from ${selectedCompany!.companyName}\'s selected bank account (Chart of Accounts)',
                          style: TextStyle(
                            color: Colors.amber.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],

          const SizedBox(height: 16),

          // Check issue date (Required)
          InkWell(
            onTap: () => _selectCheckIssueDate(context),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'Check Issue Date *',
                hintText: 'Select check issue date',
                border: const OutlineInputBorder(),
                suffixIcon: const Icon(Icons.calendar_today),
                errorText: selectedMethod == PaymentMethod.check &&
                        selectedCheckIssueDate == null
                    ? 'Check issue date is required'
                    : null,
              ),
              child: Text(
                selectedCheckIssueDate != null
                    ? _formatDate(selectedCheckIssueDate!)
                    : 'Select date',
                style: TextStyle(
                  color: selectedCheckIssueDate != null
                      ? Colors.black87
                      : Colors.grey,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Check expiry date
          InkWell(
            onTap: () => _selectCheckExpiryDate(context),
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'Check Expiry Date',
                hintText: 'Select check expiry date (optional)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              child: Text(
                selectedCheckExpiryDate != null
                    ? _formatDate(selectedCheckExpiryDate!)
                    : 'Select date (optional)',
                style: TextStyle(
                  color: selectedCheckExpiryDate != null
                      ? Colors.black87
                      : Colors.grey,
                ),
              ),
            ),
          ),
        ],

        // Account transfer specific fields
        if (selectedMethod == PaymentMethod.accountTransfer) ...[
          AssetAccountDropdown(
            labelText: 'Select Source Account',
            hintText: 'Choose the account to transfer from',
            selectedAccount: selectedChartOfAccountsAccount,
            onChanged: (account) {
              setState(() {
                selectedChartOfAccountsAccount = account;
                // Update legacy fields for backward compatibility
                selectedAccount = account != null
                    ? AccountModel(
                        id: account.id,
                        name: account.accountName,
                        accountNumber: account.accountNumber,
                        initialBalance: account.balance,
                        availableBalance: account.balance,
                        branchCode: '',
                        branchAddress: '',
                        createdAt: account.createdAt,
                        uid: account.uid,
                      )
                    : null;
              });
            },
            isRequired: true,
            validator: (value) {
              if (selectedMethod == PaymentMethod.accountTransfer &&
                  value == null) {
                return 'Please select an account';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // File attachment field (UI only, functionality not implemented yet)
          OutlinedButton.icon(
            icon: const Icon(Icons.attach_file),
            label: const Text('Attach File (Receipt/Proof)'),
            onPressed: () {
              // File picker would be implemented here
              SnackbarUtils.showInfo(
                'Coming Soon',
                'File attachment functionality will be available soon',
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildFuelCardFields() {
    // Get unique fuel stations (not just companies)
    final stations =
        widget.fuelCards.map((card) => card.fuelStationName).toSet().toList();

    // If no stations available, show a message
    if (stations.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fuel Card Details',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 10),
          Card(
            color: Colors.amber[100],
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    'No Fuel Cards Available',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Please set up fuel cards in the Fuel Card Management section before using them for payments.',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }

    // First filter by station, then by company
    final filteredCards = selectedFuelStation != null
        ? widget.fuelCards
            .where((card) => card.fuelStationName == selectedFuelStation)
            .toList()
        : widget.fuelCards;

    // Get companies for the selected station
    final companies = selectedFuelStation != null
        ? filteredCards.map((card) => card.companyName).toSet().toList()
        : [];

    // Further filter by company
    final finalFilteredCards =
        selectedFuelStation != null && selectedFuelCompany != null
            ? filteredCards
                .where((card) => card.companyName == selectedFuelCompany)
                .toList()
            : filteredCards;

    // Calculate fuel cost based on liters and current rate
    double fuelCost = 0.0;
    if (selectedFuelCard != null && fuelLitersController.text.isNotEmpty) {
      final liters = double.tryParse(fuelLitersController.text) ?? 0;
      fuelCost = liters * selectedFuelCard!.currentRate;

      // Update amount field
      if (fuelCost > 0) {
        amountController.text = fuelCost.toString();
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Fuel Card Details',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 10),

        // Step 1: Fuel station selection
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'Fuel Station',
            border: OutlineInputBorder(),
            hintText: 'Select a fuel station first',
          ),
          value: selectedFuelStation,
          items: stations.map((station) {
            return DropdownMenuItem<String>(
              value: station,
              child: Text(station),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              selectedFuelStation = value;
              selectedFuelCompany = null; // Reset company selection
              selectedFuelCard = null; // Reset card selection
              fuelLitersController.clear(); // Clear liters
              amountController.clear(); // Clear amount
            });
          },
          validator: (value) {
            if (selectedMethod == PaymentMethod.fuelCard && value == null) {
              return 'Please select a fuel station';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // Step 2: Fuel company selection for the selected station
        if (selectedFuelStation != null && companies.isNotEmpty) ...[
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Fuel Card Company',
              border: OutlineInputBorder(),
              hintText: 'Select the card provider',
            ),
            value: selectedFuelCompany,
            items: companies.map((company) {
              return DropdownMenuItem<String>(
                value: company,
                child: Text(company),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                selectedFuelCompany = value;
                selectedFuelCard = null; // Reset card selection
                fuelLitersController.clear(); // Clear liters
                amountController.clear(); // Clear amount
              });
            },
            validator: (value) {
              if (selectedMethod == PaymentMethod.fuelCard &&
                  selectedFuelStation != null &&
                  value == null) {
                return 'Please select a fuel company';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
        ],

        // Step 3: Fuel card selection when station and company are selected
        if (selectedFuelStation != null && selectedFuelCompany != null) ...[
          if (finalFilteredCards.isEmpty) ...[
            Card(
              color: Colors.red[100],
              child: const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'No cards available for this selection. Please try another station or company.',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ),
          ] else ...[
            DropdownButtonFormField<FuelCardModel>(
              decoration: const InputDecoration(
                labelText: 'Select Fuel Card',
                border: OutlineInputBorder(),
                hintText: 'Choose a specific card',
              ),
              value: selectedFuelCard,
              items: finalFilteredCards.map((card) {
                return DropdownMenuItem<FuelCardModel>(
                  value: card,
                  child: Text(
                    '${card.cardNumber} (${card.remainingCapacity.toStringAsFixed(2)} L remaining)',
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedFuelCard = value;

                  // Recalculate fuel cost if liters already entered
                  if (selectedFuelCard != null &&
                      fuelLitersController.text.isNotEmpty) {
                    final liters =
                        double.tryParse(fuelLitersController.text) ?? 0;
                    final cost = liters * selectedFuelCard!.currentRate;
                    amountController.text = cost.toString();
                  }
                });
              },
              validator: (value) {
                if (selectedMethod == PaymentMethod.fuelCard &&
                    selectedFuelStation != null &&
                    selectedFuelCompany != null &&
                    value == null) {
                  return 'Please select a fuel card';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Fuel liters
            TextFormField(
              controller: fuelLitersController,
              decoration: const InputDecoration(
                labelText: 'Fuel Liters',
                hintText: 'Enter fuel amount in liters',
                border: OutlineInputBorder(),
                suffixText: 'L',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (selectedMethod == PaymentMethod.fuelCard &&
                    selectedFuelCard != null &&
                    (value == null || value.isEmpty)) {
                  return 'Fuel amount is required';
                }
                if (value != null && value.isNotEmpty) {
                  final liters = double.tryParse(value);
                  if (liters == null) {
                    return 'Please enter a valid number';
                  }

                  // Check if enough fuel is available
                  if (selectedFuelCard != null &&
                      liters > selectedFuelCard!.remainingCapacity) {
                    return 'Not enough fuel available on this card (${selectedFuelCard!.remainingCapacity.toStringAsFixed(2)} L remaining)';
                  }
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  // Update amount based on liters and rate
                  if (selectedFuelCard != null && value.isNotEmpty) {
                    final liters = double.tryParse(value) ?? 0;
                    final cost = liters * selectedFuelCard!.currentRate;
                    amountController.text = cost.toString();
                  }
                });
              },
            ),

            const SizedBox(height: 8),

            // Show fuel cost calculation
            if (selectedFuelCard != null &&
                fuelLitersController.text.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current fuel rate: PKR ${selectedFuelCard!.currentRate.toStringAsFixed(2)}/L',
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Total cost: PKR ${fuelCost.toStringAsFixed(2)}',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ],
    );
  }

  String _getCalculatedStatus() {
    final amount = double.tryParse(amountController.text) ?? 0;
    final remainingAfterPayment =
        widget.totalFreight - widget.settledFreight - amount;

    if (remainingAfterPayment <= 0) {
      return 'PAID';
    } else {
      return 'PARTIAL';
    }
  }

  Color _getStatusColor() {
    final status = _getCalculatedStatus();
    return status == 'PAID' ? Colors.green : Colors.amber.shade700;
  }

  String _getStatusDescription() {
    final amount = double.tryParse(amountController.text) ?? 0;
    final remainingAfterPayment =
        widget.totalFreight - widget.settledFreight - amount;

    if (remainingAfterPayment <= 0) {
      return 'This payment will complete the freight payment';
    } else {
      return 'Remaining amount: PKR ${remainingAfterPayment.toStringAsFixed(2)}';
    }
  }

  void _saveTransaction() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Additional validation for check payments
    if (selectedMethod == PaymentMethod.check) {
      if (selectedCheckIssueDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Check issue date is required'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Validate cheque type specific requirements
      if (selectedChequeType == 'Other') {
        if (selectedCompany == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a company for other company cheque'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
        if (selectedAccount == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Please select an account from the external company'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      } else {
        // For own cheques
        if (selectedAccount == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please select a bank account'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }
    }

    final amount = double.tryParse(amountController.text) ?? 0;
    final remainingAfterPayment =
        widget.totalFreight - widget.settledFreight - amount;

    log('🔍 DEBUG: PaymentDialog - Creating PaymentTransactionModel');
    log('🔍 DEBUG: PaymentDialog - Amount from controller: $amount');
    log('🔍 DEBUG: PaymentDialog - Controller text: "${amountController.text}"');
    log('🔍 DEBUG: PaymentDialog - Total freight: ${widget.totalFreight}');
    log('🔍 DEBUG: PaymentDialog - Settled freight: ${widget.settledFreight}');
    log('🔍 DEBUG: PaymentDialog - Remaining after payment: $remainingAfterPayment');

    // Auto-determine status based on payment amount
    final calculatedStatus =
        remainingAfterPayment <= 0 ? PaymentStatus.paid : PaymentStatus.partial;

    // Prepare notes with cross-company information if applicable
    String finalNotes = notesController.text;
    if (selectedMethod == PaymentMethod.check &&
        selectedChequeType == 'Other' &&
        selectedCompany != null) {
      final crossCompanyNote =
          "Cross-company cheque: Used ${selectedCompany!.companyName}'s cheque for amount ${amount.toStringAsFixed(2)}";
      finalNotes = finalNotes.isEmpty
          ? crossCompanyNote
          : "$finalNotes. $crossCompanyNote";
    }

    // Create transaction object
    final transaction = PaymentTransactionModel(
      id: widget.transaction?.id ?? const Uuid().v4(),
      voucherId: widget.voucherId,
      method: selectedMethod,
      status: calculatedStatus,
      amount: amount,
      pendingAmount: remainingAfterPayment > 0 ? remainingAfterPayment : 0,
      transactionDate: selectedTransactionDate,
      notes: finalNotes,

      // Method-specific fields - Updated for Chart of Accounts integration
      accountId: (selectedMethod == PaymentMethod.accountTransfer ||
              selectedMethod == PaymentMethod.check)
          ? selectedChartOfAccountsAccount?.id ?? selectedAccount?.id
          : null,
      accountName: (selectedMethod == PaymentMethod.accountTransfer ||
              selectedMethod == PaymentMethod.check)
          ? selectedChartOfAccountsAccount?.accountName ?? selectedAccount?.name
          : null,

      checkNumber: selectedMethod == PaymentMethod.check
          ? checkNumberController.text
          : null,
      bankName: selectedMethod == PaymentMethod.check
          ? selectedChartOfAccountsAccount?.accountName ??
              selectedAccount?.name // Use Chart of Accounts name
          : null,
      checkIssueDate:
          selectedMethod == PaymentMethod.check ? selectedCheckIssueDate : null,
      checkExpiryDate: selectedMethod == PaymentMethod.check
          ? selectedCheckExpiryDate
          : null,

      fuelCardId: selectedMethod == PaymentMethod.fuelCard
          ? selectedFuelCard?.id
          : null,
      fuelCardNumber: selectedMethod == PaymentMethod.fuelCard
          ? selectedFuelCard?.cardNumber
          : null,
      fuelCompany:
          selectedMethod == PaymentMethod.fuelCard ? selectedFuelCompany : null,
      fuelLiters: selectedMethod == PaymentMethod.fuelCard
          ? double.tryParse(fuelLitersController.text)
          : null,
      fuelRate: selectedMethod == PaymentMethod.fuelCard
          ? selectedFuelCard?.currentRate
          : null,

      attachmentUrl: attachmentUrl,
    );

    log('✅ DEBUG: PaymentDialog - Created PaymentTransactionModel with amount: ${transaction.amount}');
    log('✅ DEBUG: PaymentDialog - Transaction method: ${transaction.method.name}');
    if (transaction.method == PaymentMethod.check) {
      log('✅ DEBUG: PaymentDialog - Check number: ${transaction.checkNumber}');
    }

    // Call the onSave callback
    widget.onSave(transaction);

    // Close the dialog
    Navigator.pop(context);
  }

  // Date picker methods
  Future<void> _selectCheckIssueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedCheckIssueDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      helpText: 'Select Check Issue Date',
    );
    if (picked != null && picked != selectedCheckIssueDate) {
      setState(() {
        selectedCheckIssueDate = picked;

        // Auto-set expiry date to 6 months from issue date if not already set
        selectedCheckExpiryDate ??= picked.add(const Duration(days: 180));
      });
    }
  }

  Future<void> _selectCheckExpiryDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedCheckExpiryDate ??
          (selectedCheckIssueDate?.add(const Duration(days: 180)) ??
              DateTime.now().add(const Duration(days: 180))),
      firstDate: selectedCheckIssueDate ?? DateTime(2020),
      lastDate: DateTime(2030),
      helpText: 'Select Check Expiry Date',
    );
    if (picked != null && picked != selectedCheckExpiryDate) {
      setState(() {
        selectedCheckExpiryDate = picked;
      });
    }
  }

  // Transaction date picker method
  Future<void> _selectTransactionDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedTransactionDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      helpText: 'Select Transaction Date',
    );
    if (picked != null && picked != selectedTransactionDate) {
      setState(() {
        selectedTransactionDate = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
