import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/services/voucher_payment_settings_service.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/voucher/presentation/widgets/chart_of_accounts_dropdown.dart';
import 'package:logestics/features/accounting/chart_of_accounts/presentation/controllers/chart_of_accounts_controller.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Widget for configuring voucher payment account settings
/// Provides a collapsible interface for setting up account mappings for all payment types
class VoucherPaymentSettingsWidget extends StatefulWidget {
  const VoucherPaymentSettingsWidget({super.key});

  @override
  State<VoucherPaymentSettingsWidget> createState() =>
      _VoucherPaymentSettingsWidgetState();
}

class _VoucherPaymentSettingsWidgetState
    extends State<VoucherPaymentSettingsWidget> {
  final VoucherPaymentSettingsService _settingsService =
      Get.find<VoucherPaymentSettingsService>();

  // Expansion state
  bool _isExpanded = false;

  // Account selections
  ChartOfAccountsModel? _brokerAccount;
  ChartOfAccountsModel? _munshianaAccount;
  ChartOfAccountsModel? _nlcAccount;
  ChartOfAccountsModel? _taxPayableAccount;
  // Tax Authority Accounts (replacing single tax receivable)
  ChartOfAccountsModel? _srbTaxAccount;
  ChartOfAccountsModel? _praTaxAccount;
  ChartOfAccountsModel? _braTaxAccount;
  ChartOfAccountsModel? _kraTaxAccount;
  ChartOfAccountsModel? _truckFareAccount;
  ChartOfAccountsModel? _netProfitAccount;

  // Loading state
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSavedSettings();
  }

  /// Load saved account settings from storage
  void _loadSavedSettings() {
    setState(() {
      _brokerAccount = _settingsService.loadBrokerAccount();
      _munshianaAccount = _settingsService.loadMunshianaAccount();
      _nlcAccount = _settingsService.loadNlcAccount();
      _taxPayableAccount = _settingsService.loadTaxPayableAccount();
      _srbTaxAccount = _settingsService.loadSrbTaxAccount();
      _praTaxAccount = _settingsService.loadPraTaxAccount();
      _braTaxAccount = _settingsService.loadBraTaxAccount();
      _kraTaxAccount = _settingsService.loadKraTaxAccount();
      _truckFareAccount = _settingsService.loadTruckFareAccount();
      _netProfitAccount = _settingsService.loadNetProfitAccount();
    });
  }

  /// Save all account settings
  Future<void> _saveAllSettings() async {
    if (!_areAllAccountsSelected()) {
      SnackbarUtils.showError(
          'Error', 'Please select accounts for all payment types');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      await _settingsService.saveBrokerAccount(_brokerAccount!);
      await _settingsService.saveMunshianaAccount(_munshianaAccount!);
      await _settingsService.saveNlcAccount(_nlcAccount!);
      await _settingsService.saveTaxPayableAccount(_taxPayableAccount!);
      await _settingsService.saveSrbTaxAccount(_srbTaxAccount!);
      await _settingsService.savePraTaxAccount(_praTaxAccount!);
      await _settingsService.saveBraTaxAccount(_braTaxAccount!);
      await _settingsService.saveKraTaxAccount(_kraTaxAccount!);
      await _settingsService.saveTruckFareAccount(_truckFareAccount!);
      await _settingsService.saveNetProfitAccount(_netProfitAccount!);

      SnackbarUtils.showSuccess(
          'Success', 'Voucher payment settings saved successfully');
      setState(() {
        _isExpanded = false; // Collapse after saving
      });
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to save settings: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Check if all required accounts are selected
  bool _areAllAccountsSelected() {
    return _brokerAccount != null &&
        _munshianaAccount != null &&
        _nlcAccount != null &&
        _taxPayableAccount != null &&
        _srbTaxAccount != null &&
        _praTaxAccount != null &&
        _braTaxAccount != null &&
        _kraTaxAccount != null &&
        _truckFareAccount != null &&
        _netProfitAccount != null;
  }

  /// Clear all settings
  Future<void> _clearAllSettings() async {
    try {
      await _settingsService.clearAllSettings();
      setState(() {
        _brokerAccount = null;
        _munshianaAccount = null;
        _nlcAccount = null;
        _taxPayableAccount = null;
        _srbTaxAccount = null;
        _praTaxAccount = null;
        _braTaxAccount = null;
        _kraTaxAccount = null;
        _truckFareAccount = null;
        _netProfitAccount = null;
      });
      SnackbarUtils.showSuccess('Success', 'All settings cleared');
    } catch (e) {
      SnackbarUtils.showError('Error', 'Failed to clear settings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isConfigured = _settingsService.areAllAccountsConfigured();

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          // Header with expand/collapse functionality
          ListTile(
            leading: Icon(
              Icons.settings,
              color: isConfigured ? Colors.green : Colors.orange,
            ),
            title: const Text(
              'Voucher Payment Settings',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              isConfigured
                  ? 'All accounts configured'
                  : 'Configure account mappings for voucher payments',
              style: TextStyle(
                color: isConfigured ? Colors.green : Colors.orange,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Debug button to reload Chart of Accounts
                IconButton(
                  onPressed: () async {
                    try {
                      final chartController =
                          Get.find<ChartOfAccountsController>();
                      await chartController.loadAllAccountsForDropdown();
                      SnackbarUtils.showSuccess(
                          'Reload', 'Chart of Accounts reloaded');
                    } catch (e) {
                      SnackbarUtils.showError('Error', 'Failed to reload: $e');
                    }
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Reload Chart of Accounts',
                  iconSize: 20,
                ),
                if (isConfigured)
                  Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20,
                  ),
                const SizedBox(width: 8),
                Icon(
                  _isExpanded ? Icons.expand_less : Icons.expand_more,
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),

          // Expandable content
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Instructions
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Configure default accounts for voucher payments. Each account will automatically receive the corresponding calculated amount when creating vouchers. The amount descriptions below show exactly which voucher amounts will be posted to each account.',
                            style: TextStyle(
                              color: theme.colorScheme.onPrimaryContainer,
                              fontSize: 13,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Account selection grid
                  _buildAccountSelectionGrid(),

                  const SizedBox(height: 20),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isSaving ? null : _saveAllSettings,
                          icon: _isSaving
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.save),
                          label:
                              Text(_isSaving ? 'Saving...' : 'Save Settings'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      OutlinedButton.icon(
                        onPressed: _isSaving ? null : _clearAllSettings,
                        icon: const Icon(Icons.clear_all),
                        label: const Text('Clear All'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build the account selection grid
  Widget _buildAccountSelectionGrid() {
    return Column(
      children: [
        // Row 1: Broker Fees & Munshiana
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'Broker Fees Account',
                hint: 'Select Liability Account',
                amountDescription: 'Broker Fee Amount will be posted here',
                selectedAccount: _brokerAccount,
                onChanged: (account) =>
                    setState(() => _brokerAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAccountDropdown(
                label: 'Munshiana Account',
                hint: 'Select Equity Account',
                amountDescription: 'Munshiana Amount will be posted here',
                selectedAccount: _munshianaAccount,
                onChanged: (account) =>
                    setState(() => _munshianaAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        EquityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Row 2: NLC & Tax Payable
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'NLC Account',
                hint: 'Select Asset Account',
                amountDescription:
                    'Company Freight (NLC) Amount will be posted here',
                selectedAccount: _nlcAccount,
                onChanged: (account) => setState(() => _nlcAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        AssetAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAccountDropdown(
                label: 'Tax Payable Account',
                hint: 'Select Liability Account',
                amountDescription:
                    '4.6% Freight Tax Amount will be posted here',
                selectedAccount: _taxPayableAccount,
                onChanged: (account) =>
                    setState(() => _taxPayableAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Row 3: SRB Tax Authority & PRA Tax Authority
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'SRB Tax Authority Account',
                hint: 'Select Liability Account',
                amountDescription:
                    'Tax amount will be posted here based on voucher selections',
                selectedAccount: _srbTaxAccount,
                onChanged: (account) =>
                    setState(() => _srbTaxAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAccountDropdown(
                label: 'PRA Tax Authority Account',
                hint: 'Select Liability Account',
                amountDescription:
                    'Tax amount will be posted here based on voucher selections',
                selectedAccount: _praTaxAccount,
                onChanged: (account) =>
                    setState(() => _praTaxAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Row 4: BRA Tax Authority & KRA Tax Authority
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'BRA Tax Authority Account',
                hint: 'Select Liability Account',
                amountDescription:
                    'Tax amount will be posted here based on voucher selections',
                selectedAccount: _braTaxAccount,
                onChanged: (account) =>
                    setState(() => _braTaxAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAccountDropdown(
                label: 'KRA Tax Authority Account',
                hint: 'Select Liability Account',
                amountDescription:
                    'Tax amount will be posted here based on voucher selections',
                selectedAccount: _kraTaxAccount,
                onChanged: (account) =>
                    setState(() => _kraTaxAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        LiabilityAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Row 5: Truck Fare Account (single column)
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'Truck Fare Account',
                hint: 'Select Liability Account',
                amountDescription:
                    'Truck/Wagon Freight Amount will be posted here',
                selectedAccount: _truckFareAccount,
                onChanged: (account) =>
                    setState(() => _truckFareAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        TruckFreightAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const Expanded(child: SizedBox()), // Empty space for alignment
          ],
        ),

        const SizedBox(height: 12),

        // Row 6: Net Profit (centered)
        Row(
          children: [
            Expanded(
              child: _buildAccountDropdown(
                label: 'Net Profit Account',
                hint: 'Select Equity Account',
                amountDescription:
                    'Calculated Net Profit Amount will be posted here',
                selectedAccount: _netProfitAccount,
                onChanged: (account) =>
                    setState(() => _netProfitAccount = account),
                dropdownBuilder:
                    (labelText, hintText, selectedAccount, onChanged) =>
                        RetainedEarningsAccountDropdown(
                  labelText: labelText,
                  hintText: hintText,
                  selectedAccount: selectedAccount,
                  onChanged: onChanged,
                  isRequired: true,
                ),
              ),
            ),
            const Expanded(child: SizedBox()), // Empty space for alignment
          ],
        ),
      ],
    );
  }

  /// Build individual account dropdown with consistent styling and amount mapping description
  Widget _buildAccountDropdown({
    required String label,
    required String hint,
    required String amountDescription,
    required ChartOfAccountsModel? selectedAccount,
    required Function(ChartOfAccountsModel?) onChanged,
    required Widget Function(String, String, ChartOfAccountsModel?,
            Function(ChartOfAccountsModel?))
        dropdownBuilder,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 2),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            amountDescription,
            style: TextStyle(
              fontSize: 11,
              color: theme.colorScheme.primary.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(height: 6),
        dropdownBuilder(label, hint, selectedAccount, onChanged),
      ],
    );
  }
}
