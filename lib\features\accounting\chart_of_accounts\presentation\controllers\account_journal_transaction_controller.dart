import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../core/utils/app_constants/texts/app_strings.dart';
import '../../../../../core/utils/mixins/pagination_mixin.dart';
import '../../../../../core/utils/snackbar_utils.dart';
import '../../../../../core/services/account_type_helper_service.dart';
import '../../../../../models/finance/chart_of_accounts_model.dart';
import '../../../../../models/finance/journal_entry_model.dart';
import '../../services/account_journal_transaction_service.dart';

class AccountJournalTransactionController extends GetxController
    with PaginationMixin {
  final AccountJournalTransactionService _transactionService;

  // Observable variables
  final transactions = <AccountJournalTransaction>[].obs;
  final isLoading = false.obs;
  final isError = false.obs;
  final errorMessage = ''.obs;

  // Pagination variables
  final isLoadingPage = false.obs;
  final hasNextPage = false.obs;
  QueryDocumentSnapshot? _lastDocument;
  String? _currentAccountId;
  String? _currentUid;

  // Filter variables
  final selectedDateRange = Rx<DateTimeRange?>(null);
  final selectedStatuses = <JournalEntryStatus>[].obs;
  final searchQuery = ''.obs;

  // Active account
  final activeAccount = Rx<ChartOfAccountsModel?>(null);

  // Account balance
  final accountBalance = 0.0.obs;
  final isLoadingBalance = false.obs;

  AccountJournalTransactionController(this._transactionService);

  @override
  void onInit() {
    super.onInit();
    // Set default pagination settings
    setItemsPerPage(25); // Default to 25 transactions per page
  }

  /// Load transactions for a specific account with pagination
  Future<void> loadTransactionsForAccount(
    ChartOfAccountsModel account,
    String uid, {
    bool isFirstPage = true,
  }) async {
    try {
      if (isFirstPage) {
        isLoading.value = true;
        _currentAccountId = account.id;
        _currentUid = uid;
        activeAccount.value = account;
        _lastDocument = null;
        transactions.clear();
        setCurrentPage(1);
      } else {
        isLoadingPage.value = true;
      }

      isError.value = false;

      // Debug: Log the status filter being applied
      final statusFilterToApply =
          selectedStatuses.isNotEmpty ? selectedStatuses : null;
      log('🔍 [CONTROLLER] Loading transactions for account: ${account.accountName}');
      log('🔍 [CONTROLLER] Selected statuses: ${selectedStatuses.map((s) => s.name).toList()}');
      log('🔍 [CONTROLLER] Status filter to apply: ${statusFilterToApply?.map((s) => s.name).toList() ?? 'null (include all)'}');

      final result = await _transactionService.getAccountTransactionsPaginated(
        accountId: account.id,
        uid: uid,
        accountCategory: account.category,
        limit: itemsPerPage.value,
        lastDocument: _lastDocument,
        startDate: selectedDateRange.value?.start,
        endDate: selectedDateRange.value?.end,
        statusFilter: statusFilterToApply,
      );

      if (isFirstPage) {
        transactions.value = result.transactions;

        // Update account balance to the final running balance from transactions
        // This ensures the current balance display matches the transaction list
        if (result.transactions.isNotEmpty) {
          // Sort by date to get the most recent transaction's running balance
          final sortedTransactions = result.transactions.toList()
            ..sort((a, b) => b.entryDate.compareTo(a.entryDate));
          accountBalance.value = sortedTransactions.first.runningBalance;
          log('Updated account balance to final running balance: ${accountBalance.value}');
        } else {
          // If no transactions, load the stored account balance
          _loadAccountBalance(account, uid);
        }
      } else {
        transactions.addAll(result.transactions);
      }

      _lastDocument = result.nextPageCursor;
      hasNextPage.value = result.hasNextPage;
    } catch (e) {
      isError.value = true;
      errorMessage.value = e.toString();
      SnackbarUtils.showError(
        AppStrings.errorS,
        'Failed to load transactions: $e',
      );
    } finally {
      isLoading.value = false;
      isLoadingPage.value = false;
    }
  }

  /// Load account balance as of current date
  Future<void> _loadAccountBalance(
      ChartOfAccountsModel account, String uid) async {
    try {
      isLoadingBalance.value = true;

      log('Loading balance for account: ${account.accountName} (${account.id})');
      log('Account stored balance: ${account.balance}');
      log('Account category: ${account.category.name}');

      // First, set the stored balance as the current balance
      accountBalance.value = account.balance;

      // Then try to get the calculated balance from journal entries
      try {
        final calculatedBalance =
            await _transactionService.getAccountBalanceAsOfDate(
          accountId: account.id,
          uid: uid,
          asOfDate: DateTime.now(),
          accountCategory: account.category,
        );

        log('Calculated balance from journal entries: $calculatedBalance');

        // Use calculated balance if it's different from stored balance
        // This helps catch any discrepancies
        if ((calculatedBalance - account.balance).abs() > 0.01) {
          log('Balance discrepancy detected! Stored: ${account.balance}, Calculated: $calculatedBalance');
          // For now, use the stored balance but log the discrepancy
          // In production, you might want to use the calculated balance or trigger a reconciliation
        }

        // Use calculated balance as it should be more accurate
        accountBalance.value = calculatedBalance;
      } catch (e) {
        log('Error calculating balance from journal entries: $e');
        // Keep using the stored balance as fallback
      }
    } catch (e) {
      // Don't show error for balance loading, just log it
      log('Error loading account balance: $e');
      // Fallback to stored balance
      accountBalance.value = account.balance;
    } finally {
      isLoadingBalance.value = false;
    }
  }

  /// Set date range filter
  void setDateRange(DateTimeRange? dateRange) {
    selectedDateRange.value = dateRange;
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Clear date range filter
  void clearDateRange() {
    selectedDateRange.value = null;
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Add status filter
  void addStatusFilter(JournalEntryStatus status) {
    if (!selectedStatuses.contains(status)) {
      selectedStatuses.add(status);
      if (_currentAccountId != null && _currentUid != null) {
        loadTransactionsForAccount(activeAccount.value!, _currentUid!,
            isFirstPage: true);
      }
    }
  }

  /// Remove status filter
  void removeStatusFilter(JournalEntryStatus status) {
    selectedStatuses.remove(status);
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Clear all status filters
  void clearStatusFilters() {
    selectedStatuses.clear();
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Set search query
  void setSearchQuery(String query) {
    searchQuery.value = query;
    // Note: Search functionality would need to be implemented in the service
    // For now, we'll just store the query
  }

  /// Get current page transactions for display
  List<AccountJournalTransaction> getCurrentPageTransactions() {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= transactions.length) {
      return [];
    }

    return transactions.sublist(
      startIndex,
      endIndex > transactions.length ? transactions.length : endIndex,
    );
  }

  /// Override pagination methods to handle loading more data
  @override
  void setCurrentPage(int page) {
    super.setCurrentPage(page);

    // Calculate if we need to load more data
    final startIndex = (page - 1) * itemsPerPage.value;
    if (startIndex >= transactions.length && hasNextPage.value) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: false);
    }
  }

  @override
  void setItemsPerPage(int count) {
    super.setItemsPerPage(count);

    // Reload data with new page size
    if (_currentAccountId != null && _currentUid != null) {
      loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Format currency amount
  String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'Rs. ', decimalDigits: 2);
    return formatter.format(amount);
  }

  /// Format date
  String formatDate(DateTime date) {
    return DateFormat('MMM dd, yyyy').format(date);
  }

  /// Format date and time
  String formatDateTime(DateTime date) {
    return DateFormat('MMM dd, yyyy HH:mm').format(date);
  }

  /// Get status color
  Color getStatusColor(JournalEntryStatus status) {
    switch (status) {
      case JournalEntryStatus.draft:
        return Colors.orange;
      case JournalEntryStatus.posted:
        return Colors.green;
      case JournalEntryStatus.reversed:
        return Colors.red;
    }
  }

  /// Get status display name
  String getStatusDisplayName(JournalEntryStatus status) {
    return status.displayName;
  }

  /// Get transaction type display (Debit/Credit)
  String getTransactionType(AccountJournalTransaction transaction) {
    return transaction.isDebit ? 'Debit' : 'Credit';
  }

  /// Get transaction type color based on balance change effect
  Color getTransactionTypeColor(AccountJournalTransaction transaction) {
    if (activeAccount.value == null) return Colors.grey;

    return AccountTypeHelperService.getBalanceChangeColorByCategory(
      accountCategory: activeAccount.value!.category,
      debitAmount: transaction.debitAmount,
      creditAmount: transaction.creditAmount,
    );
  }

  /// Refresh transactions
  Future<void> refreshTransactions() async {
    if (_currentAccountId != null && _currentUid != null) {
      await loadTransactionsForAccount(activeAccount.value!, _currentUid!,
          isFirstPage: true);
    }
  }

  /// Get total transaction count
  int get totalTransactionCount => transactions.length;

  /// Check if there are any transactions
  bool get hasTransactions => transactions.isNotEmpty;

  /// Check if filters are applied
  bool get hasFiltersApplied =>
      selectedDateRange.value != null ||
      selectedStatuses.isNotEmpty ||
      searchQuery.value.isNotEmpty;
}
