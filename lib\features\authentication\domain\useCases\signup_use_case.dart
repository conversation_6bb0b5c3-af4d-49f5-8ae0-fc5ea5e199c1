import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/models/auth_failer.dart';
import 'package:logestics/features/authentication/domain/repositories/auth_repository.dart';
import 'package:logestics/models/user_model.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';

class SignupUseCase {
  final AuthRepository authRepository;
  final FirebaseAuth firebaseAuth;

  SignupUseCase({
    required this.authRepository,
    FirebaseAuth? firebaseAuth,
  }) : firebaseAuth = firebaseAuth ?? FirebaseAuth.instance;

  Future<Either<AuthFailure, UserCredential>> execute(
      String email, String password, UserModel userModel) async {
    try {
      print('🔄 SignupUseCase: Starting registration for email: $email');

      // 1. Create the user account with Firebase Auth
      print('🔄 SignupUseCase: Creating Firebase Auth user...');
      final userCredential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      print(
          '✅ SignupUseCase: Firebase Auth user created successfully: ${userCredential.user?.uid}');

      // 2. Update the user model with the created user ID
      final updatedUserModel = userModel.copyWith(
        uid: userCredential.user!.uid,
      );
      print(
          '🔄 SignupUseCase: Updated user model with UID: ${updatedUserModel.uid}');

      // 3. Save the user details to Firestore using UserModel pattern
      print('🔄 SignupUseCase: Saving user to Firestore...');
      await _createUser(updatedUserModel);
      print('✅ SignupUseCase: User saved to Firestore successfully');

      return Right(userCredential);
    } on FirebaseAuthException catch (e) {
      // Handle Firebase Auth-specific errors
      switch (e.code) {
        case 'email-already-in-use':
          return Left(
            AuthFailure(
              field: 'email',
              code: e.code,
              message: 'This email is already registered.',
            ),
          );
        case 'invalid-email':
          return Left(
            AuthFailure(
              field: 'email',
              code: e.code,
              message: 'Please enter a valid email address.',
            ),
          );
        case 'weak-password':
          return Left(
            AuthFailure(
              field: 'password',
              code: e.code,
              message: 'The password provided is too weak.',
            ),
          );
        default:
          return Left(
            AuthFailure(
              code: e.code,
              message: e.message ?? 'An authentication error occurred.',
            ),
          );
      }
    } catch (e) {
      return Left(
        AuthFailure(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  // Create user in Firestore according to UserModel pattern
  Future<void> _createUser(UserModel user) async {
    try {
      print('🔄 _createUser: Starting Firestore user creation...');
      print('🔍 _createUser: User data: ${user.toJson()}');
      print('🔍 _createUser: Collection: ${AppCollection.usersCollection}');
      print('🔍 _createUser: Document ID: ${user.uid}');

      // Create a batch operation for transaction safety
      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      // Set the user document with the user's UID as the document ID
      final userRef =
          firestore.collection(AppCollection.usersCollection).doc(user.uid);
      print('🔍 _createUser: Document reference created: ${userRef.path}');

      batch.set(userRef, user.toJson());
      print('🔄 _createUser: Batch operation prepared, committing...');

      // Commit the batch
      await batch.commit();
      print('✅ _createUser: Batch committed successfully');
    } catch (e) {
      print('❌ _createUser: Error occurred: $e');
      print('❌ _createUser: Error type: ${e.runtimeType}');
      // Let the error propagate up so it can be handled by the execute method
      rethrow;
    }
  }
}
