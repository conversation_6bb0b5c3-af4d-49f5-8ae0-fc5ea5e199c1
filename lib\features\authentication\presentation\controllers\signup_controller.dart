import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/authentication/domain/useCases/signup_use_case.dart';
import 'package:logestics/models/user_model.dart';

class SignupController extends GetxController {
  final SignupUseCase _signupUseCase;

  SignupController({required SignupUseCase signupUseCase})
      : _signupUseCase = signupUseCase;

  // Form key
  late final GlobalKey<FormState> signupFormStateKey;

  // Track disposal state to prevent double disposal
  bool _isDisposed = false;

  // Text controllers - updated for UserModel
  late final companyNameController = TextEditingController()
    ..addListener(_updateState);
  late final emailController = TextEditingController()
    ..addListener(_updateState);
  late final phoneController = TextEditingController()
    ..addListener(_updateState);
  late final passwordController = TextEditingController()
    ..addListener(_updateState);
  late final confirmPasswordController = TextEditingController()
    ..addListener(_updateState);

  // Observable values
  final errorMessages = <String, String>{}.obs;
  var isShowPassword = false.obs;
  var isShowConfirmPassword = false.obs;
  var isLoading = false.obs;
  var password = ''.obs;
  var confirmPassword = ''.obs;

  // Focus states - updated for UserModel
  RxBool companyNameFocus = false.obs;
  RxBool emailFocus = false.obs;
  RxBool phoneFocus = false.obs;
  RxBool passwordFocus = false.obs;
  RxBool confirmPasswordFocus = false.obs;

  // Navigation methods
  void navigateToLogin() {
    Get.back(); // Go back to login screen
  }

  void navigateToHome() {
    // Don't manually navigate - let FirebaseAuthService handle it
    // The auth state listener will automatically navigate to home
  }

  @override
  void onInit() {
    super.onInit();
    signupFormStateKey = GlobalKey<FormState>();
  }

  void _updateState() {
    password.value = passwordController.text;
    confirmPassword.value = confirmPasswordController.text;
  }

  // Toggle password visibility
  void toggleShowPassword() {
    isShowPassword.value = !isShowPassword.value;
  }

  void toggleShowConfirmPassword() {
    isShowConfirmPassword.value = !isShowConfirmPassword.value;
  }

  // Focus management - updated for UserModel
  void onTapOutside(BuildContext context) {
    companyNameFocus.value = false;
    emailFocus.value = false;
    phoneFocus.value = false;
    passwordFocus.value = false;
    confirmPasswordFocus.value = false;
    FocusScope.of(context).unfocus();
  }

  void onFocusCompanyName() {
    companyNameFocus.value = true;
    emailFocus.value = false;
    phoneFocus.value = false;
    passwordFocus.value = false;
    confirmPasswordFocus.value = false;
  }

  void onFocusEmail() {
    companyNameFocus.value = false;
    emailFocus.value = true;
    phoneFocus.value = false;
    passwordFocus.value = false;
    confirmPasswordFocus.value = false;
  }

  void onFocusPhone() {
    companyNameFocus.value = false;
    emailFocus.value = false;
    phoneFocus.value = true;
    passwordFocus.value = false;
    confirmPasswordFocus.value = false;
  }

  void onFocusPassword() {
    companyNameFocus.value = false;
    emailFocus.value = false;
    phoneFocus.value = false;
    passwordFocus.value = true;
    confirmPasswordFocus.value = false;
  }

  void onFocusConfirmPassword() {
    companyNameFocus.value = false;
    emailFocus.value = false;
    phoneFocus.value = false;
    passwordFocus.value = false;
    confirmPasswordFocus.value = true;
  }

  // Validation
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.emailEmpty;
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(value)) {
      return AppStrings.emailInvalid;
    }

    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.passwordEmpty;
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }

    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    // Check for at least one number
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != passwordController.text) {
      return 'Passwords do not match';
    }

    return null;
  }

  // Registration logic - updated for UserModel
  Future<void> register() async {
    try {
      // Validate inputs first
      final companyNameValidation =
          validateRequired(companyNameController.text, 'Company name');
      if (companyNameValidation != null) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          'Company name is required',
        );
        return;
      }

      final emailValidation = validateEmail(emailController.text);
      if (emailValidation != null) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          emailValidation,
        );
        return;
      }

      final passwordValidation = validatePassword(passwordController.text);
      if (passwordValidation != null) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          passwordValidation,
        );
        return;
      }

      final confirmPasswordValidation =
          validateConfirmPassword(confirmPasswordController.text);
      if (confirmPasswordValidation != null) {
        SnackbarUtils.showError(
          AppStrings.errorS,
          confirmPasswordValidation,
        );
        return;
      }

      // Set loading state
      isLoading.value = true;
      errorMessages.clear();

      final email = emailController.text.trim();
      final password = passwordController.text.trim();

      // Create the new user model with a temporary uid that will be updated after auth
      final userModel = UserModel(
        uid: "", // Temporary uid that will be replaced with Firebase uid
        email: email,
        companyName: companyNameController.text.trim(),
        phoneNumber: phoneController.text.trim(),
      );

      // Call signup use case
      final result = await _signupUseCase.execute(email, password, userModel);

      result.fold(
        (failure) {
          // Handle registration failure
          if (failure.field != null) {
            errorMessages[failure.field!] = failure.message;
          } else {
            errorMessages['general'] = failure.message;
          }
          isLoading.value = false;

          // Show error snackbar for better user feedback
          SnackbarUtils.showError(
            'Registration Failed',
            failure.message,
          );
        },
        (userCredential) {
          // Update the user model with the actual Firebase uid
          final updatedUserModel = userModel.copyWith(
            uid: userCredential.user?.uid ?? userModel.uid,
          );

          // Success
          errorMessages.clear();
          isLoading.value = false;

          // Show success message
          SnackbarUtils.showSuccess(
            'Registration Successful',
            'Welcome ${updatedUserModel.companyName}! Your account has been created.',
          );

          // Clear form data
          companyNameController.clear();
          emailController.clear();
          phoneController.clear();
          passwordController.clear();
          confirmPasswordController.clear();

          // Don't manually navigate - let FirebaseAuthService handle it
          // The auth state listener will automatically navigate to home
        },
      );
    } catch (e) {
      // Handle unexpected errors
      final errorMessage = 'An unexpected error occurred: ${e.toString()}';
      errorMessages['general'] = errorMessage;
      isLoading.value = false;

      // Show error snackbar
      SnackbarUtils.showError(
        'Registration Error',
        errorMessage,
      );
    }
  }

  @override
  void onClose() {
    if (!_isDisposed) {
      _isDisposed = true;

      companyNameController.removeListener(_updateState);
      emailController.removeListener(_updateState);
      phoneController.removeListener(_updateState);
      passwordController.removeListener(_updateState);
      confirmPasswordController.removeListener(_updateState);

      companyNameController.dispose();
      emailController.dispose();
      phoneController.dispose();
      passwordController.dispose();
      confirmPasswordController.dispose();
    }
    super.onClose();
  }
}
