import 'dart:developer';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';

/// Service for managing persistent voucher payment account settings
/// Uses GetStorage to store and retrieve account mappings for voucher payment types
class VoucherPaymentSettingsService extends GetxService {
  static const String _storageKey = 'voucher_payment_settings';

  late final GetStorage _storage;

  // Storage keys for each payment type
  static const String _brokerAccountKey = 'broker_account';
  static const String _munshianaAccountKey = 'munshiana_account';
  static const String _nlcAccountKey = 'nlc_account';
  static const String _taxPayableAccountKey = 'tax_payable_account';
  // Tax Authority Accounts (replacing single tax receivable)
  static const String _srbTaxAccountKey = 'srb_tax_account';
  static const String _praTaxAccountKey = 'pra_tax_account';
  static const String _braTaxAccountKey = 'bra_tax_account';
  static const String _kraTaxAccountKey = 'kra_tax_account';
  static const String _truckFareAccountKey = 'truck_fare_account';
  static const String _netProfitAccountKey = 'net_profit_account';

  @override
  Future<void> onInit() async {
    super.onInit();
    _storage = GetStorage();
    log('VoucherPaymentSettingsService initialized');
  }

  /// Save broker fee account setting
  Future<void> saveBrokerAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_brokerAccountKey', account.toJson());
      log('Saved broker account: ${account.displayName}');
    } catch (e) {
      log('Error saving broker account: $e');
      rethrow;
    }
  }

  /// Load broker fee account setting
  ChartOfAccountsModel? loadBrokerAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_brokerAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded broker account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading broker account: $e');
      return null;
    }
  }

  /// Save munshiana account setting
  Future<void> saveMunshianaAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_munshianaAccountKey', account.toJson());
      log('Saved munshiana account: ${account.displayName}');
    } catch (e) {
      log('Error saving munshiana account: $e');
      rethrow;
    }
  }

  /// Load munshiana account setting
  ChartOfAccountsModel? loadMunshianaAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_munshianaAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded munshiana account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading munshiana account: $e');
      return null;
    }
  }

  /// Save NLC account setting
  Future<void> saveNlcAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write('${_storageKey}_$_nlcAccountKey', account.toJson());
      log('Saved NLC account: ${account.displayName}');
    } catch (e) {
      log('Error saving NLC account: $e');
      rethrow;
    }
  }

  /// Load NLC account setting
  ChartOfAccountsModel? loadNlcAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_nlcAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded NLC account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading NLC account: $e');
      return null;
    }
  }

  /// Save tax payable account setting
  Future<void> saveTaxPayableAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_taxPayableAccountKey', account.toJson());
      log('Saved tax payable account: ${account.displayName}');
    } catch (e) {
      log('Error saving tax payable account: $e');
      rethrow;
    }
  }

  /// Load tax payable account setting
  ChartOfAccountsModel? loadTaxPayableAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_taxPayableAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded tax payable account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading tax payable account: $e');
      return null;
    }
  }

  /// Save SRB tax authority account setting
  Future<void> saveSrbTaxAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_srbTaxAccountKey', account.toJson());
      log('Saved SRB tax account: ${account.displayName}');
    } catch (e) {
      log('Error saving SRB tax account: $e');
      rethrow;
    }
  }

  /// Load SRB tax authority account setting
  ChartOfAccountsModel? loadSrbTaxAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_srbTaxAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded SRB tax account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading SRB tax account: $e');
      return null;
    }
  }

  /// Save PRA tax authority account setting
  Future<void> savePraTaxAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_praTaxAccountKey', account.toJson());
      log('Saved PRA tax account: ${account.displayName}');
    } catch (e) {
      log('Error saving PRA tax account: $e');
      rethrow;
    }
  }

  /// Load PRA tax authority account setting
  ChartOfAccountsModel? loadPraTaxAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_praTaxAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded PRA tax account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading PRA tax account: $e');
      return null;
    }
  }

  /// Save BRA tax authority account setting
  Future<void> saveBraTaxAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_braTaxAccountKey', account.toJson());
      log('Saved BRA tax account: ${account.displayName}');
    } catch (e) {
      log('Error saving BRA tax account: $e');
      rethrow;
    }
  }

  /// Load BRA tax authority account setting
  ChartOfAccountsModel? loadBraTaxAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_braTaxAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded BRA tax account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading BRA tax account: $e');
      return null;
    }
  }

  /// Save KRA tax authority account setting
  Future<void> saveKraTaxAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_kraTaxAccountKey', account.toJson());
      log('Saved KRA tax account: ${account.displayName}');
    } catch (e) {
      log('Error saving KRA tax account: $e');
      rethrow;
    }
  }

  /// Load KRA tax authority account setting
  ChartOfAccountsModel? loadKraTaxAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_kraTaxAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded KRA tax account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading KRA tax account: $e');
      return null;
    }
  }

  /// Save truck fare account setting
  Future<void> saveTruckFareAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_truckFareAccountKey', account.toJson());
      log('Saved truck fare account: ${account.displayName}');
    } catch (e) {
      log('Error saving truck fare account: $e');
      rethrow;
    }
  }

  /// Load truck fare account setting
  ChartOfAccountsModel? loadTruckFareAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_truckFareAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded truck fare account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading truck fare account: $e');
      return null;
    }
  }

  /// Save net profit account setting
  Future<void> saveNetProfitAccount(ChartOfAccountsModel account) async {
    try {
      await _storage.write(
          '${_storageKey}_$_netProfitAccountKey', account.toJson());
      log('Saved net profit account: ${account.displayName}');
    } catch (e) {
      log('Error saving net profit account: $e');
      rethrow;
    }
  }

  /// Load net profit account setting
  ChartOfAccountsModel? loadNetProfitAccount() {
    try {
      final data = _storage.read('${_storageKey}_$_netProfitAccountKey');
      if (data != null) {
        final account =
            ChartOfAccountsModel.fromJson(Map<String, dynamic>.from(data));
        log('Loaded net profit account: ${account.displayName}');
        return account;
      }
      return null;
    } catch (e) {
      log('Error loading net profit account: $e');
      return null;
    }
  }

  /// Load all saved account settings
  Map<String, ChartOfAccountsModel?> loadAllAccountSettings() {
    final settings = {
      'broker': loadBrokerAccount(),
      'munshiana': loadMunshianaAccount(),
      'nlc': loadNlcAccount(),
      'taxPayable': loadTaxPayableAccount(),
      'srbTax': loadSrbTaxAccount(),
      'praTax': loadPraTaxAccount(),
      'braTax': loadBraTaxAccount(),
      'kraTax': loadKraTaxAccount(),
      'truckFare': loadTruckFareAccount(),
      'netProfit': loadNetProfitAccount(),
    };

    return settings;
  }

  /// Check if all required account settings are configured
  bool areAllAccountsConfigured() {
    final settings = loadAllAccountSettings();
    return settings.values.every((account) => account != null);
  }

  /// Clear all saved account settings
  Future<void> clearAllSettings() async {
    try {
      await _storage.remove('${_storageKey}_$_brokerAccountKey');
      await _storage.remove('${_storageKey}_$_munshianaAccountKey');
      await _storage.remove('${_storageKey}_$_nlcAccountKey');
      await _storage.remove('${_storageKey}_$_taxPayableAccountKey');
      await _storage.remove('${_storageKey}_$_srbTaxAccountKey');
      await _storage.remove('${_storageKey}_$_praTaxAccountKey');
      await _storage.remove('${_storageKey}_$_braTaxAccountKey');
      await _storage.remove('${_storageKey}_$_kraTaxAccountKey');
      await _storage.remove('${_storageKey}_$_truckFareAccountKey');
      await _storage.remove('${_storageKey}_$_netProfitAccountKey');
      log('Cleared all voucher payment settings');
    } catch (e) {
      log('Error clearing voucher payment settings: $e');
      rethrow;
    }
  }
}
